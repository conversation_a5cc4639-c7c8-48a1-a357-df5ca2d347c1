/**
 * 测试地图服务配置
 * 验证所有地图服务的URL是否可访问
 */

const { mapServices } = require('../lib/map-services.ts')

async function testMapService(service) {
  console.log(`\n测试地图服务: ${service.name}`)
  console.log(`描述: ${service.description}`)
  console.log(`瓦片URL: ${service.tileUrl}`)
  console.log(`子域名: ${service.subdomains.join(', ')}`)
  console.log(`缩放范围: ${service.minZoom || 1} - ${service.maxZoom}`)
  
  // 构建测试URL（使用第一个子域名和中等缩放级别）
  const testUrl = service.tileUrl
    .replace('{s}', service.subdomains[0] || '')
    .replace('{z}', '10')
    .replace('{x}', '512')
    .replace('{y}', '384')
    .replace('{r}', '')
  
  console.log(`测试URL: ${testUrl}`)
  
  try {
    const response = await fetch(testUrl, { 
      method: 'HEAD',
      timeout: 5000 
    })
    console.log(`✅ 状态: ${response.status} ${response.statusText}`)
  } catch (error) {
    console.log(`❌ 错误: ${error.message}`)
  }
}

async function testAllServices() {
  console.log('🗺️  开始测试所有地图服务...\n')
  
  for (const service of mapServices) {
    await testMapService(service)
    await new Promise(resolve => setTimeout(resolve, 1000)) // 延迟1秒避免请求过快
  }
  
  console.log('\n✨ 测试完成！')
}

// 如果直接运行此脚本
if (require.main === module) {
  testAllServices().catch(console.error)
}

module.exports = { testMapService, testAllServices }
