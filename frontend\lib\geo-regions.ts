/**
 * 地理区域检测和地图服务智能切换
 * 根据地理位置自动选择最适合的地图服务
 */

export interface GeoBounds {
  north: number
  south: number
  east: number
  west: number
}

export interface GeoRegion {
  id: string
  name: string
  bounds: GeoBounds
  preferredServices: string[] // 按优先级排序的地图服务ID
  description: string
}

/**
 * 地理区域定义
 * 简化配置，统一使用高德地图
 */
export const geoRegions: GeoRegion[] = [
  {
    id: "china-mainland",
    name: "中国大陆",
    bounds: {
      north: 54.0,
      south: 18.0,
      east: 135.0,
      west: 73.0
    },
    preferredServices: ["amap"],
    description: "中国大陆区域，使用高德地图获得最佳的本土化体验"
  }
]

/**
 * 检测坐标点是否在指定区域内
 */
export function isPointInRegion(lat: number, lng: number, region: GeoRegion): boolean {
  const { bounds } = region
  return lat >= bounds.south && 
         lat <= bounds.north && 
         lng >= bounds.west && 
         lng <= bounds.east
}

/**
 * 检测地图边界框与区域的重叠程度
 */
export function calculateRegionOverlap(mapBounds: GeoBounds, region: GeoRegion): number {
  const { bounds } = region
  
  // 计算重叠区域
  const overlapNorth = Math.min(mapBounds.north, bounds.north)
  const overlapSouth = Math.max(mapBounds.south, bounds.south)
  const overlapEast = Math.min(mapBounds.east, bounds.east)
  const overlapWest = Math.max(mapBounds.west, bounds.west)
  
  // 如果没有重叠，返回0
  if (overlapNorth <= overlapSouth || overlapEast <= overlapWest) {
    return 0
  }
  
  // 计算重叠面积
  const overlapArea = (overlapNorth - overlapSouth) * (overlapEast - overlapWest)
  
  // 计算地图视图面积
  const mapArea = (mapBounds.north - mapBounds.south) * (mapBounds.east - mapBounds.west)
  
  // 返回重叠比例
  return overlapArea / mapArea
}

/**
 * 根据地图边界框检测最佳地理区域
 */
export function detectBestRegion(mapBounds: GeoBounds): GeoRegion | null {
  let bestRegion: GeoRegion | null = null
  let maxOverlap = 0
  
  for (const region of geoRegions) {
    const overlap = calculateRegionOverlap(mapBounds, region)
    if (overlap > maxOverlap && overlap > 0.3) { // 至少30%重叠才考虑
      maxOverlap = overlap
      bestRegion = region
    }
  }
  
  return bestRegion
}

/**
 * 根据中心点检测地理区域（备用方法）
 */
export function detectRegionByCenter(lat: number, lng: number): GeoRegion | null {
  for (const region of geoRegions) {
    if (isPointInRegion(lat, lng, region)) {
      return region
    }
  }
  return null
}

/**
 * 获取区域的推荐地图服务
 */
export function getRecommendedService(region: GeoRegion | null, availableServices: string[]): string | null {
  if (!region) return null
  
  // 找到第一个可用的推荐服务
  for (const serviceId of region.preferredServices) {
    if (availableServices.includes(serviceId)) {
      return serviceId
    }
  }
  
  return null
}

/**
 * 格式化区域切换提示信息
 */
export function formatRegionSwitchMessage(region: GeoRegion, serviceName: string): string {
  return `已切换到${serviceName}以获得${region.name}区域的最佳显示效果`
}
