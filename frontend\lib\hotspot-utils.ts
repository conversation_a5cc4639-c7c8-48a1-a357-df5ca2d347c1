import { ColorGrade, GridCell, MapBounds, HotspotData } from './hotspot-types';

// eBird 风格的颜色分级配置
export const COLOR_GRADES: ColorGrade[] = [
  { min: 0, max: 10, color: '#FFEDA0', label: '0-10' },
  { min: 11, max: 50, color: '#FED976', label: '11-50' },
  { min: 51, max: 100, color: '#FEB24C', label: '51-100' },
  { min: 101, max: 150, color: '#FD8D3C', label: '101-150' },
  { min: 151, max: 200, color: '#FC4E2A', label: '151-200' },
  { min: 201, max: 300, color: '#E31A1C', label: '201-300' },
  { min: 301, max: 500, color: '#BD0026', label: '301-500' },
  { min: 501, max: Infinity, color: '#800026', label: '500+' },
];

// 根据热点数量获取颜色
export function getColorByCount(count: number): string {
  const grade = COLOR_GRADES.find(g => count >= g.min && count <= g.max);
  return grade?.color || COLOR_GRADES[0].color;
}

// 生成网格
export function generateGrid(bounds: MapBounds, gridSize: number): GridCell[] {
  const cells: GridCell[] = [];

  // 计算网格数量
  const latSteps = Math.ceil((bounds.north - bounds.south) / gridSize);
  const lngSteps = Math.ceil((bounds.east - bounds.west) / gridSize);

  // 限制最大网格数量以提高性能
  const maxCells = 10000; // 最多10000个网格
  const totalCells = latSteps * lngSteps;

  if (totalCells > maxCells) {
    // 如果网格太多，增加网格大小
    const scaleFactor = Math.sqrt(totalCells / maxCells);
    const adjustedGridSize = gridSize * scaleFactor;
    return generateGrid(bounds, adjustedGridSize);
  }

  for (let i = 0; i < latSteps; i++) {
    for (let j = 0; j < lngSteps; j++) {
      const south = bounds.south + i * gridSize;
      const north = Math.min(bounds.north, south + gridSize);
      const west = bounds.west + j * gridSize;
      const east = Math.min(bounds.east, west + gridSize);

      const cell: GridCell = {
        id: `cell-${i}-${j}`,
        bounds: { north, south, east, west },
        center: {
          lat: (north + south) / 2,
          lng: (east + west) / 2,
        },
        hotspotCount: 0,
        color: COLOR_GRADES[0].color,
      };

      cells.push(cell);
    }
  }

  return cells;
}

// 将热点数据分配到网格中
export function assignHotspotsToGrid(
  cells: GridCell[],
  hotspots: HotspotData[]
): GridCell[] {
  // 重置所有网格的热点数量
  cells.forEach(cell => {
    cell.hotspotCount = 0;
  });
  
  // 将热点分配到对应的网格
  hotspots.forEach(hotspot => {
    const cell = cells.find(c => 
      hotspot.lat >= c.bounds.south &&
      hotspot.lat < c.bounds.north &&
      hotspot.lng >= c.bounds.west &&
      hotspot.lng < c.bounds.east
    );
    
    if (cell) {
      cell.hotspotCount += hotspot.count;
    }
  });
  
  // 更新网格颜色
  cells.forEach(cell => {
    cell.color = getColorByCount(cell.hotspotCount);
  });
  
  return cells;
}

// 生成模拟热点数据
export function generateMockHotspots(bounds: MapBounds, count: number): HotspotData[] {
  const hotspots: HotspotData[] = [];

  // 创建一些热点聚集区域
  const hotspotClusters = Math.min(10, Math.max(3, Math.floor(count / 100)));
  const clusterCenters: { lat: number; lng: number }[] = [];

  // 生成聚集中心点
  for (let i = 0; i < hotspotClusters; i++) {
    clusterCenters.push({
      lat: bounds.south + Math.random() * (bounds.north - bounds.south),
      lng: bounds.west + Math.random() * (bounds.east - bounds.west),
    });
  }

  for (let i = 0; i < count; i++) {
    let lat: number, lng: number;

    // 70% 的热点围绕聚集中心生成，30% 随机分布
    if (Math.random() < 0.7 && clusterCenters.length > 0) {
      const cluster = clusterCenters[Math.floor(Math.random() * clusterCenters.length)];
      const radius = 0.05; // 聚集半径
      const angle = Math.random() * 2 * Math.PI;
      const distance = Math.random() * radius;

      lat = cluster.lat + Math.cos(angle) * distance;
      lng = cluster.lng + Math.sin(angle) * distance;

      // 确保在边界内
      lat = Math.max(bounds.south, Math.min(bounds.north, lat));
      lng = Math.max(bounds.west, Math.min(bounds.east, lng));
    } else {
      // 随机分布
      lat = bounds.south + Math.random() * (bounds.north - bounds.south);
      lng = bounds.west + Math.random() * (bounds.east - bounds.west);
    }

    // 使用更真实的热点数量分布（大多数热点数量较少）
    let hotspotCount: number;
    const rand = Math.random();
    if (rand < 0.4) {
      hotspotCount = Math.floor(Math.random() * 20) + 1; // 1-20
    } else if (rand < 0.7) {
      hotspotCount = Math.floor(Math.random() * 50) + 21; // 21-70
    } else if (rand < 0.9) {
      hotspotCount = Math.floor(Math.random() * 100) + 71; // 71-170
    } else {
      hotspotCount = Math.floor(Math.random() * 400) + 171; // 171-570
    }

    hotspots.push({
      id: `hotspot-${i}`,
      lat,
      lng,
      count: hotspotCount,
    });
  }

  return hotspots;
}

// 格式化数字显示
export function formatCount(count: number): string {
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`;
  }
  return count.toString();
}

// 根据缩放级别计算合适的网格大小
export function getGridSizeByZoom(zoom: number): number {
  // 缩放级别越高，网格越小，显示越精细
  if (zoom >= 12) return 0.01;   // 高缩放：0.01度网格 (~1km)
  if (zoom >= 10) return 0.02;   // 中高缩放：0.02度网格 (~2km)
  if (zoom >= 8) return 0.05;    // 中缩放：0.05度网格 (~5km)
  if (zoom >= 6) return 0.1;     // 中低缩放：0.1度网格 (~10km)
  if (zoom >= 4) return 0.2;     // 低缩放：0.2度网格 (~20km)
  return 0.5;                    // 很低缩放：0.5度网格 (~50km)
}

// 计算地图边界
export function calculateBounds(center: [number, number], zoom: number): MapBounds {
  // 根据缩放级别计算合适的边界
  const latRange = 180 / Math.pow(2, zoom - 1);
  const lngRange = 360 / Math.pow(2, zoom - 1);

  return {
    north: center[0] + latRange / 2,
    south: center[0] - latRange / 2,
    east: center[1] + lngRange / 2,
    west: center[1] - lngRange / 2,
  };
}
