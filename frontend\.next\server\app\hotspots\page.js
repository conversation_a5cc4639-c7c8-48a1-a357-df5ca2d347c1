/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/hotspots/page";
exports.ids = ["app/hotspots/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"83487451e814\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFx3b3Jrc3BhY2VzXFxkZFxcbWJkcFxcZnJvbnRlbmRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4MzQ4NzQ1MWU4MTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/hotspots/page.tsx":
/*!*******************************!*\
  !*** ./app/hotspots/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\workspaces\\dd\\mbdp\\frontend\\app\\hotspots\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var leaflet_dist_leaflet_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! leaflet/dist/leaflet.css */ \"(rsc)/./node_modules/.pnpm/leaflet@1.9.4/node_modules/leaflet/dist/leaflet.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"国家海洋生物声学数据平台\",\n    description: \"专业的海洋生物声学数据管理与分析平台\",\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\workspaces\\dd\\mbdp\\frontend\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fhotspots%2Fpage&page=%2Fhotspots%2Fpage&appPaths=%2Fhotspots%2Fpage&pagePath=private-next-app-dir%2Fhotspots%2Fpage.tsx&appDir=C%3A%5Cworkspaces%5Cdd%5Cmbdp%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cworkspaces%5Cdd%5Cmbdp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fhotspots%2Fpage&page=%2Fhotspots%2Fpage&appPaths=%2Fhotspots%2Fpage&pagePath=private-next-app-dir%2Fhotspots%2Fpage.tsx&appDir=C%3A%5Cworkspaces%5Cdd%5Cmbdp%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cworkspaces%5Cdd%5Cmbdp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?1689\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/hotspots/page.tsx */ \"(rsc)/./app/hotspots/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'hotspots',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/hotspots/page\",\n        pathname: \"/hotspots\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fhotspots%2Fpage&page=%2Fhotspots%2Fpage&appPaths=%2Fhotspots%2Fpage&pagePath=private-next-app-dir%2Fhotspots%2Fpage.tsx&appDir=C%3A%5Cworkspaces%5Cdd%5Cmbdp%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cworkspaces%5Cdd%5Cmbdp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Chotspots%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Chotspots%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/hotspots/page.tsx */ \"(rsc)/./app/hotspots/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUN3b3Jrc3BhY2VzJTVDJTVDZGQlNUMlNUNtYmRwJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNob3RzcG90cyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBZ0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXHdvcmtzcGFjZXNcXFxcZGRcXFxcbWJkcFxcXFxmcm9udGVuZFxcXFxhcHBcXFxcaG90c3BvdHNcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Chotspots%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cleaflet%401.9.4%5C%5Cnode_modules%5C%5Cleaflet%5C%5Cdist%5C%5Cleaflet.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cleaflet%401.9.4%5C%5Cnode_modules%5C%5Cleaflet%5C%5Cdist%5C%5Cleaflet.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUN3b3Jrc3BhY2VzJTVDJTVDZGQlNUMlNUNtYmRwJTVDJTVDZnJvbnRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDdGhlbWUtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDd29ya3NwYWNlcyU1QyU1Q2RkJTVDJTVDbWJkcCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40X3JlYWN0LWRvbSU0MDE5LjEuMF9yZWFjdCU0MDE5LjEuMF9fcmVhY3QlNDAxOS4xLjAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q3dvcmtzcGFjZXMlNUMlNUNkZCU1QyU1Q21iZHAlNUMlNUNmcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUN3b3Jrc3BhY2VzJTVDJTVDZGQlNUMlNUNtYmRwJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q2xlYWZsZXQlNDAxLjkuNCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q2xlYWZsZXQlNUMlNUNkaXN0JTVDJTVDbGVhZmxldC5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUEwSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVGhlbWVQcm92aWRlclwiXSAqLyBcIkM6XFxcXHdvcmtzcGFjZXNcXFxcZGRcXFxcbWJkcFxcXFxmcm9udGVuZFxcXFxjb21wb25lbnRzXFxcXHRoZW1lLXByb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cleaflet%401.9.4%5C%5Cnode_modules%5C%5Cleaflet%5C%5Cdist%5C%5Cleaflet.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/hotspots/page.tsx":
/*!*******************************!*\
  !*** ./app/hotspots/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HotspotsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _components_search_bar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/search-bar */ \"(ssr)/./components/search-bar.tsx\");\n/* harmony import */ var _components_hotspot_legend__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/hotspot-legend */ \"(ssr)/./components/hotspot-legend.tsx\");\n/* harmony import */ var _components_map_controls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/map-controls */ \"(ssr)/./components/map-controls.tsx\");\n/* harmony import */ var _components_info_panel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/info-panel */ \"(ssr)/./components/info-panel.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bird,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bird.js\");\n/* harmony import */ var _barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bird,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bird,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// 动态导入地图组件以避免 SSR 问题\nconst HotspotMap = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\hotspots\\\\page.tsx -> \" + \"@/components/hotspot-map\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full bg-gray-100 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 text-gray-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"加载地图组件中...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 5\n        }, undefined)\n});\nfunction HotspotsPage() {\n    // 地图状态\n    const [mapCenter, setMapCenter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        39.9042,\n        116.4074\n    ]); // 北京\n    const [mapZoom, setMapZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(8);\n    const [layerType, setLayerType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('street');\n    // UI 状态\n    const [hoveredCell, setHoveredCell] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 统计数据（模拟）\n    const [totalHotspots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(12847);\n    const [visibleHotspots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3421);\n    // 处理地图移动\n    const handleMapMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HotspotsPage.useCallback[handleMapMove]\": (center, zoom)=>{\n            setMapCenter(center);\n            setMapZoom(zoom);\n        }\n    }[\"HotspotsPage.useCallback[handleMapMove]\"], []);\n    // 处理位置搜索\n    const handleLocationSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HotspotsPage.useCallback[handleLocationSelect]\": (location)=>{\n            setMapCenter([\n                location.lat,\n                location.lng\n            ]);\n            setMapZoom(12);\n        }\n    }[\"HotspotsPage.useCallback[handleLocationSelect]\"], []);\n    // 处理网格悬停\n    const handleGridCellHover = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HotspotsPage.useCallback[handleGridCellHover]\": (cell)=>{\n            setHoveredCell(cell);\n        }\n    }[\"HotspotsPage.useCallback[handleGridCellHover]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"观鸟热点\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block text-sm text-gray-500\",\n                                        children: \"发现最佳观鸟地点\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 max-w-lg mx-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_search_bar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onLocationSelect: handleLocationSelect,\n                                    placeholder: \"搜索地点或坐标...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                className: \"lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 30\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 58\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-[calc(100vh-120px)] relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HotspotMap, {\n                                center: mapCenter,\n                                zoom: mapZoom,\n                                layerType: layerType,\n                                onMapMove: handleMapMove,\n                                onGridCellHover: handleGridCellHover,\n                                className: \"w-full h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: `\n          w-80 bg-white border-l border-gray-200 p-4 space-y-4 overflow-y-auto\n          ${sidebarOpen ? 'block' : 'hidden lg:block'}\n          lg:relative absolute right-0 top-16 bottom-0 z-10 shadow-lg lg:shadow-none\n        `,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_info_panel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                hoveredCell: hoveredCell,\n                                totalHotspots: totalHotspots,\n                                visibleHotspots: visibleHotspots\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hotspot_legend__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_controls__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                currentLayer: layerType,\n                                onLayerChange: setLayerType\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-4 border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-blue-900 mb-2\",\n                                        children: \"使用说明\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-blue-800 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 拖拽地图查看不同区域\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 悬停网格查看热点数量\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 使用搜索框快速定位\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 切换地图类型获得更好视觉效果\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-5\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/hotspots/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/hotspot-legend.tsx":
/*!***************************************!*\
  !*** ./components/hotspot-legend.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HotspotLegend: () => (/* binding */ HotspotLegend),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hotspot-utils */ \"(ssr)/./lib/hotspot-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ HotspotLegend,default auto */ \n\n\nfunction HotspotLegend({ className = '' }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white rounded-lg shadow-lg p-4 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-semibold text-gray-700 mb-3\",\n                children: \"观察热点数量\"\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: _lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.COLOR_GRADES.map((grade, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 rounded-sm border border-gray-300\",\n                                style: {\n                                    backgroundColor: grade.color\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-600 font-medium\",\n                                children: grade.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-3 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"每个网格显示该区域内的热点数量\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HotspotLegend);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/hotspot-legend.tsx\n");

/***/ }),

/***/ "(ssr)/./components/info-panel.tsx":
/*!***********************************!*\
  !*** ./components/info-panel.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfoPanel: () => (/* binding */ InfoPanel),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hotspot-utils */ \"(ssr)/./lib/hotspot-utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,MapPin!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,MapPin!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ InfoPanel,default auto */ \n\n\n\nfunction InfoPanel({ hoveredCell, totalHotspots, visibleHotspots, className = '' }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white rounded-lg shadow-lg p-4 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-semibold text-gray-700 mb-3 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    \"热点信息\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            hoveredCell ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-blue-50 rounded-lg border border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-blue-900 mb-1\",\n                                        children: \"网格区域\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-700 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"中心: \",\n                                                    hoveredCell.center.lat.toFixed(4),\n                                                    \", \",\n                                                    hoveredCell.center.lng.toFixed(4)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"范围: \",\n                                                    hoveredCell.bounds.south.toFixed(4),\n                                                    \" ~ \",\n                                                    hoveredCell.bounds.north.toFixed(4),\n                                                    \"\\xb0N\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6\",\n                                                children: [\n                                                    hoveredCell.bounds.west.toFixed(4),\n                                                    \" ~ \",\n                                                    hoveredCell.bounds.east.toFixed(4),\n                                                    \"\\xb0E\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"热点数量\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-lg text-gray-900\",\n                                        children: (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.formatCount)(hoveredCell.hotspotCount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"颜色等级\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 rounded border border-gray-300\",\n                                                style: {\n                                                    backgroundColor: hoveredCell.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: hoveredCell.hotspotCount > 500 ? '500+' : hoveredCell.hotspotCount > 300 ? '301-500' : hoveredCell.hotspotCount > 200 ? '201-300' : hoveredCell.hotspotCount > 150 ? '151-200' : hoveredCell.hotspotCount > 100 ? '101-150' : hoveredCell.hotspotCount > 50 ? '51-100' : hoveredCell.hotspotCount > 10 ? '11-50' : '0-10'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-6 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"将鼠标悬停在网格上\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: \"查看详细信息\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"总热点数\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.formatCount)(totalHotspots)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"可见热点\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.formatCount)(visibleHotspots)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-3 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500 space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"• 网格大小: 0.1\\xb0 \\xd7 0.1\\xb0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"• 数据更新: 实时\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"• 点击网格可查看详情\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InfoPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/info-panel.tsx\n");

/***/ }),

/***/ "(ssr)/./components/map-controls.tsx":
/*!*************************************!*\
  !*** ./components/map-controls.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MapControls: () => (/* binding */ MapControls),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Layers,Map,Mountain,Satellite!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Layers,Map,Mountain,Satellite!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/satellite.js\");\n/* harmony import */ var _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Layers,Map,Mountain,Satellite!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mountain.js\");\n/* harmony import */ var _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Layers,Map,Mountain,Satellite!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* __next_internal_client_entry_do_not_use__ MapControls,default auto */ \n\n\nconst layerOptions = [\n    {\n        type: 'street',\n        label: '街道图',\n        icon: _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        description: '标准街道地图'\n    },\n    {\n        type: 'satellite',\n        label: '卫星图',\n        icon: _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        description: '卫星影像'\n    },\n    {\n        type: 'terrain',\n        label: '地形图',\n        icon: _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        description: '地形地貌'\n    },\n    {\n        type: 'hybrid',\n        label: '混合图',\n        icon: _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        description: '卫星图+标注'\n    }\n];\nfunction MapControls({ currentLayer, onLayerChange, className = '' }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white rounded-lg shadow-lg p-4 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-semibold text-gray-700 mb-3\",\n                children: \"地图类型\"\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: layerOptions.map((option)=>{\n                    const IconComponent = option.icon;\n                    const isSelected = currentLayer === option.type;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onLayerChange(option.type),\n                        className: `w-full flex items-center space-x-3 p-2 rounded-md text-left transition-colors\n                        ${isSelected ? 'bg-blue-50 text-blue-700 border border-blue-200' : 'hover:bg-gray-50 text-gray-700 border border-transparent'}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `p-1 rounded ${isSelected ? 'bg-blue-100' : 'bg-gray-100'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-sm\",\n                                        children: option.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: option.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 15\n                            }, this),\n                            isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, option.type, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-3 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-xs text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"网格工具\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"已启用\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MapControls);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/map-controls.tsx\n");

/***/ }),

/***/ "(ssr)/./components/search-bar.tsx":
/*!***********************************!*\
  !*** ./components/search-bar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: () => (/* binding */ SearchBar),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin,Search,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin,Search,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin,Search,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ SearchBar,default auto */ \n\n\n// 模拟搜索结果\nconst mockSearchResults = [\n    {\n        id: '1',\n        name: '北京市',\n        lat: 39.9042,\n        lng: 116.4074,\n        type: 'city'\n    },\n    {\n        id: '2',\n        name: '上海市',\n        lat: 31.2304,\n        lng: 121.4737,\n        type: 'city'\n    },\n    {\n        id: '3',\n        name: '广州市',\n        lat: 23.1291,\n        lng: 113.2644,\n        type: 'city'\n    },\n    {\n        id: '4',\n        name: '深圳市',\n        lat: 22.5431,\n        lng: 114.0579,\n        type: 'city'\n    },\n    {\n        id: '5',\n        name: '杭州市',\n        lat: 30.2741,\n        lng: 120.1551,\n        type: 'city'\n    },\n    {\n        id: '6',\n        name: '南京市',\n        lat: 32.0603,\n        lng: 118.7969,\n        type: 'city'\n    },\n    {\n        id: '7',\n        name: '成都市',\n        lat: 30.5728,\n        lng: 104.0668,\n        type: 'city'\n    },\n    {\n        id: '8',\n        name: '西安市',\n        lat: 34.3416,\n        lng: 108.9398,\n        type: 'city'\n    }\n];\nfunction SearchBar({ onLocationSelect, placeholder = '输入地点名称或坐标...', className = '' }) {\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const searchRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 搜索函数\n    const search = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SearchBar.useCallback[search]\": (searchQuery)=>{\n            if (!searchQuery.trim()) {\n                setResults([]);\n                return;\n            }\n            // 模拟搜索延迟\n            const filteredResults = mockSearchResults.filter({\n                \"SearchBar.useCallback[search].filteredResults\": (result)=>result.name.toLowerCase().includes(searchQuery.toLowerCase())\n            }[\"SearchBar.useCallback[search].filteredResults\"]);\n            setResults(filteredResults);\n        }\n    }[\"SearchBar.useCallback[search]\"], []);\n    // 处理输入变化\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setQuery(value);\n        search(value);\n        setIsOpen(true);\n        setSelectedIndex(-1);\n    };\n    // 处理选择结果\n    const handleSelectResult = (result)=>{\n        setQuery(result.name);\n        setIsOpen(false);\n        setResults([]);\n        onLocationSelect({\n            lat: result.lat,\n            lng: result.lng,\n            name: result.name\n        });\n    };\n    // 处理键盘事件\n    const handleKeyDown = (e)=>{\n        if (!isOpen || results.length === 0) return;\n        switch(e.key){\n            case 'ArrowDown':\n                e.preventDefault();\n                setSelectedIndex((prev)=>prev < results.length - 1 ? prev + 1 : prev);\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                setSelectedIndex((prev)=>prev > 0 ? prev - 1 : -1);\n                break;\n            case 'Enter':\n                e.preventDefault();\n                if (selectedIndex >= 0 && selectedIndex < results.length) {\n                    handleSelectResult(results[selectedIndex]);\n                }\n                break;\n            case 'Escape':\n                setIsOpen(false);\n                setSelectedIndex(-1);\n                break;\n        }\n    };\n    // 清除搜索\n    const clearSearch = ()=>{\n        setQuery('');\n        setResults([]);\n        setIsOpen(false);\n        setSelectedIndex(-1);\n        inputRef.current?.focus();\n    };\n    // 点击外部关闭\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchBar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"SearchBar.useEffect.handleClickOutside\": (event)=>{\n                    if (searchRef.current && !searchRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                        setSelectedIndex(-1);\n                    }\n                }\n            }[\"SearchBar.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"SearchBar.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"SearchBar.useEffect\"];\n        }\n    }[\"SearchBar.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: searchRef,\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"h-4 w-4 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: inputRef,\n                        type: \"text\",\n                        value: query,\n                        onChange: handleInputChange,\n                        onKeyDown: handleKeyDown,\n                        onFocus: ()=>query && setIsOpen(true),\n                        placeholder: placeholder,\n                        className: \"block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg  focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-sm placeholder-gray-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    query && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearSearch,\n                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4 text-gray-400 hover:text-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            isOpen && results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto\",\n                children: results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleSelectResult(result),\n                        className: `w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3\n                        ${index === selectedIndex ? 'bg-blue-50 text-blue-700' : 'text-gray-700'}\n                        ${index === 0 ? 'rounded-t-lg' : ''}\n                        ${index === results.length - 1 ? 'rounded-b-lg' : 'border-b border-gray-100'}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: result.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            result.lat.toFixed(4),\n                                            \", \",\n                                            result.lng.toFixed(4)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, result.id, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchBar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/search-bar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/.pnpm/next-themes@0.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505/node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBSVY7QUFFYixTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkM6XFx3b3Jrc3BhY2VzXFxkZFxcbWJkcFxcZnJvbnRlbmRcXGNvbXBvbmVudHNcXHRoZW1lLXByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQge1xuICBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlcixcbiAgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMsXG59IGZyb20gJ25leHQtdGhlbWVzJ1xuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/hotspot-utils.ts":
/*!******************************!*\
  !*** ./lib/hotspot-utils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLOR_GRADES: () => (/* binding */ COLOR_GRADES),\n/* harmony export */   assignHotspotsToGrid: () => (/* binding */ assignHotspotsToGrid),\n/* harmony export */   calculateBounds: () => (/* binding */ calculateBounds),\n/* harmony export */   formatCount: () => (/* binding */ formatCount),\n/* harmony export */   generateGrid: () => (/* binding */ generateGrid),\n/* harmony export */   generateMockHotspots: () => (/* binding */ generateMockHotspots),\n/* harmony export */   getColorByCount: () => (/* binding */ getColorByCount)\n/* harmony export */ });\n// eBird 风格的颜色分级配置\nconst COLOR_GRADES = [\n    {\n        min: 0,\n        max: 10,\n        color: '#FFEDA0',\n        label: '0-10'\n    },\n    {\n        min: 11,\n        max: 50,\n        color: '#FED976',\n        label: '11-50'\n    },\n    {\n        min: 51,\n        max: 100,\n        color: '#FEB24C',\n        label: '51-100'\n    },\n    {\n        min: 101,\n        max: 150,\n        color: '#FD8D3C',\n        label: '101-150'\n    },\n    {\n        min: 151,\n        max: 200,\n        color: '#FC4E2A',\n        label: '151-200'\n    },\n    {\n        min: 201,\n        max: 300,\n        color: '#E31A1C',\n        label: '201-300'\n    },\n    {\n        min: 301,\n        max: 500,\n        color: '#BD0026',\n        label: '301-500'\n    },\n    {\n        min: 501,\n        max: Infinity,\n        color: '#800026',\n        label: '500+'\n    }\n];\n// 根据热点数量获取颜色\nfunction getColorByCount(count) {\n    const grade = COLOR_GRADES.find((g)=>count >= g.min && count <= g.max);\n    return grade?.color || COLOR_GRADES[0].color;\n}\n// 生成网格\nfunction generateGrid(bounds, gridSize) {\n    const cells = [];\n    // 计算网格数量\n    const latSteps = Math.ceil((bounds.north - bounds.south) / gridSize);\n    const lngSteps = Math.ceil((bounds.east - bounds.west) / gridSize);\n    for(let i = 0; i < latSteps; i++){\n        for(let j = 0; j < lngSteps; j++){\n            const south = bounds.south + i * gridSize;\n            const north = Math.min(bounds.north, south + gridSize);\n            const west = bounds.west + j * gridSize;\n            const east = Math.min(bounds.east, west + gridSize);\n            const cell = {\n                id: `cell-${i}-${j}`,\n                bounds: {\n                    north,\n                    south,\n                    east,\n                    west\n                },\n                center: {\n                    lat: (north + south) / 2,\n                    lng: (east + west) / 2\n                },\n                hotspotCount: 0,\n                color: COLOR_GRADES[0].color\n            };\n            cells.push(cell);\n        }\n    }\n    return cells;\n}\n// 将热点数据分配到网格中\nfunction assignHotspotsToGrid(cells, hotspots) {\n    // 重置所有网格的热点数量\n    cells.forEach((cell)=>{\n        cell.hotspotCount = 0;\n    });\n    // 将热点分配到对应的网格\n    hotspots.forEach((hotspot)=>{\n        const cell = cells.find((c)=>hotspot.lat >= c.bounds.south && hotspot.lat < c.bounds.north && hotspot.lng >= c.bounds.west && hotspot.lng < c.bounds.east);\n        if (cell) {\n            cell.hotspotCount += hotspot.count;\n        }\n    });\n    // 更新网格颜色\n    cells.forEach((cell)=>{\n        cell.color = getColorByCount(cell.hotspotCount);\n    });\n    return cells;\n}\n// 生成模拟热点数据\nfunction generateMockHotspots(bounds, count) {\n    const hotspots = [];\n    for(let i = 0; i < count; i++){\n        const lat = bounds.south + Math.random() * (bounds.north - bounds.south);\n        const lng = bounds.west + Math.random() * (bounds.east - bounds.west);\n        const hotspotCount = Math.floor(Math.random() * 100) + 1;\n        hotspots.push({\n            id: `hotspot-${i}`,\n            lat,\n            lng,\n            count: hotspotCount\n        });\n    }\n    return hotspots;\n}\n// 格式化数字显示\nfunction formatCount(count) {\n    if (count >= 1000) {\n        return `${(count / 1000).toFixed(1)}k`;\n    }\n    return count.toString();\n}\n// 计算地图边界\nfunction calculateBounds(center, zoom) {\n    // 根据缩放级别计算合适的边界\n    const latRange = 180 / Math.pow(2, zoom - 1);\n    const lngRange = 360 / Math.pow(2, zoom - 1);\n    return {\n        north: center[0] + latRange / 2,\n        south: center[0] - latRange / 2,\n        east: center[1] + lngRange / 2,\n        west: center[1] - lngRange / 2\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/hotspot-utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Chotspots%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Chotspots%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/hotspots/page.tsx */ \"(ssr)/./app/hotspots/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUN3b3Jrc3BhY2VzJTVDJTVDZGQlNUMlNUNtYmRwJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNob3RzcG90cyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBZ0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXHdvcmtzcGFjZXNcXFxcZGRcXFxcbWJkcFxcXFxmcm9udGVuZFxcXFxhcHBcXFxcaG90c3BvdHNcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Chotspots%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cleaflet%401.9.4%5C%5Cnode_modules%5C%5Cleaflet%5C%5Cdist%5C%5Cleaflet.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cleaflet%401.9.4%5C%5Cnode_modules%5C%5Cleaflet%5C%5Cdist%5C%5Cleaflet.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUN3b3Jrc3BhY2VzJTVDJTVDZGQlNUMlNUNtYmRwJTVDJTVDZnJvbnRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDdGhlbWUtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDd29ya3NwYWNlcyU1QyU1Q2RkJTVDJTVDbWJkcCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40X3JlYWN0LWRvbSU0MDE5LjEuMF9yZWFjdCU0MDE5LjEuMF9fcmVhY3QlNDAxOS4xLjAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q3dvcmtzcGFjZXMlNUMlNUNkZCU1QyU1Q21iZHAlNUMlNUNmcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUN3b3Jrc3BhY2VzJTVDJTVDZGQlNUMlNUNtYmRwJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q2xlYWZsZXQlNDAxLjkuNCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q2xlYWZsZXQlNUMlNUNkaXN0JTVDJTVDbGVhZmxldC5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUEwSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVGhlbWVQcm92aWRlclwiXSAqLyBcIkM6XFxcXHdvcmtzcGFjZXNcXFxcZGRcXFxcbWJkcFxcXFxmcm9udGVuZFxcXFxjb21wb25lbnRzXFxcXHRoZW1lLXByb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cleaflet%401.9.4%5C%5Cnode_modules%5C%5Cleaflet%5C%5Cdist%5C%5Cleaflet.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/next-themes@0.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/leaflet@1.9.4","vendor-chunks/lucide-react@0.454.0_react@19.1.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fhotspots%2Fpage&page=%2Fhotspots%2Fpage&appPaths=%2Fhotspots%2Fpage&pagePath=private-next-app-dir%2Fhotspots%2Fpage.tsx&appDir=C%3A%5Cworkspaces%5Cdd%5Cmbdp%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cworkspaces%5Cdd%5Cmbdp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();