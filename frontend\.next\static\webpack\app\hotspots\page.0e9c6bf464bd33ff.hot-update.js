"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hotspots/page",{

/***/ "(app-pages-browser)/./app/hotspots/page.tsx":
/*!*******************************!*\
  !*** ./app/hotspots/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HotspotsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _components_search_bar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/search-bar */ \"(app-pages-browser)/./components/search-bar.tsx\");\n/* harmony import */ var _components_hotspot_legend__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/hotspot-legend */ \"(app-pages-browser)/./components/hotspot-legend.tsx\");\n/* harmony import */ var _components_map_controls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/map-controls */ \"(app-pages-browser)/./components/map-controls.tsx\");\n/* harmony import */ var _components_info_panel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/info-panel */ \"(app-pages-browser)/./components/info-panel.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bird,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bird.js\");\n/* harmony import */ var _barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bird,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bird,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// 动态导入地图组件以避免 SSR 问题\nconst HotspotMap = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_hotspot-map_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/hotspot-map */ \"(app-pages-browser)/./components/hotspot-map.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\hotspots\\\\page.tsx -> \" + \"@/components/hotspot-map\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full bg-gray-100 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 text-gray-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"加载地图组件中...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 5\n        }, undefined)\n});\n_c = HotspotMap;\nfunction HotspotsPage() {\n    _s();\n    // 地图状态\n    const [mapCenter, setMapCenter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        39.9042,\n        116.4074\n    ]); // 北京\n    const [mapZoom, setMapZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(8);\n    const [layerType, setLayerType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('street');\n    // UI 状态\n    const [hoveredCell, setHoveredCell] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 统计数据（模拟）\n    const [totalHotspots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(12847);\n    const [visibleHotspots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3421);\n    // 处理地图移动\n    const handleMapMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HotspotsPage.useCallback[handleMapMove]\": (center, zoom)=>{\n            setMapCenter(center);\n            setMapZoom(zoom);\n        }\n    }[\"HotspotsPage.useCallback[handleMapMove]\"], []);\n    // 处理位置搜索\n    const handleLocationSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HotspotsPage.useCallback[handleLocationSelect]\": (location)=>{\n            setMapCenter([\n                location.lat,\n                location.lng\n            ]);\n            setMapZoom(12);\n        }\n    }[\"HotspotsPage.useCallback[handleLocationSelect]\"], []);\n    // 处理网格悬停\n    const handleGridCellHover = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HotspotsPage.useCallback[handleGridCellHover]\": (cell)=>{\n            setHoveredCell(cell);\n        }\n    }[\"HotspotsPage.useCallback[handleGridCellHover]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"观鸟热点\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block text-sm text-gray-500\",\n                                        children: \"发现最佳观鸟地点\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 max-w-lg mx-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_search_bar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onLocationSelect: handleLocationSelect,\n                                    placeholder: \"搜索地点或坐标...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                className: \"lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 30\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bird_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 58\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-[calc(100vh-120px)] relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HotspotMap, {\n                                center: mapCenter,\n                                zoom: mapZoom,\n                                layerType: layerType,\n                                onMapMove: handleMapMove,\n                                onGridCellHover: handleGridCellHover,\n                                className: \"w-full h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"\\n          w-80 bg-white border-l border-gray-200 p-4 space-y-4 overflow-y-auto\\n          \".concat(sidebarOpen ? 'block' : 'hidden lg:block', \"\\n          lg:relative absolute right-0 top-16 bottom-0 z-10 shadow-lg lg:shadow-none\\n        \"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_info_panel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                hoveredCell: hoveredCell,\n                                totalHotspots: totalHotspots,\n                                visibleHotspots: visibleHotspots,\n                                currentZoom: mapZoom\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hotspot_legend__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_controls__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                currentLayer: layerType,\n                                onLayerChange: setLayerType\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-4 border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-blue-900 mb-2\",\n                                        children: \"使用说明\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-blue-800 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 拖拽地图查看不同区域\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 悬停网格查看热点数量\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 使用搜索框快速定位\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 切换地图类型获得更好视觉效果\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-5\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\hotspots\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(HotspotsPage, \"NZkxPrz/8j+5aYuJ0kJJbO9S2Ng=\");\n_c1 = HotspotsPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"HotspotMap\");\n$RefreshReg$(_c1, \"HotspotsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/hotspots/page.tsx\n"));

/***/ })

});