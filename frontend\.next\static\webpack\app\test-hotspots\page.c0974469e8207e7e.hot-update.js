"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-hotspots/page",{

/***/ "(app-pages-browser)/./components/info-panel.tsx":
/*!***********************************!*\
  !*** ./components/info-panel.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfoPanel: () => (/* binding */ InfoPanel),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hotspot-utils */ \"(app-pages-browser)/./lib/hotspot-utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ InfoPanel,default auto */ \n\n\n\nfunction InfoPanel(param) {\n    let { hoveredCell, totalHotspots, visibleHotspots, currentZoom = 8, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-4 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-semibold text-gray-700 mb-3 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    \"热点信息\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            hoveredCell ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-blue-50 rounded-lg border border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-blue-900 mb-1\",\n                                        children: \"网格区域\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-700 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                        lineNumber: 40,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"中心: \",\n                                                    hoveredCell.center.lat.toFixed(4),\n                                                    \", \",\n                                                    hoveredCell.center.lng.toFixed(4)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"范围: \",\n                                                    hoveredCell.bounds.south.toFixed(4),\n                                                    \" ~ \",\n                                                    hoveredCell.bounds.north.toFixed(4),\n                                                    \"\\xb0N\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6\",\n                                                children: [\n                                                    hoveredCell.bounds.west.toFixed(4),\n                                                    \" ~ \",\n                                                    hoveredCell.bounds.east.toFixed(4),\n                                                    \"\\xb0E\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"热点数量\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-lg text-gray-900\",\n                                        children: (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.formatCount)(hoveredCell.hotspotCount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"颜色等级\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 rounded border border-gray-300\",\n                                                style: {\n                                                    backgroundColor: hoveredCell.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: hoveredCell.hotspotCount > 500 ? '500+' : hoveredCell.hotspotCount > 300 ? '301-500' : hoveredCell.hotspotCount > 200 ? '201-300' : hoveredCell.hotspotCount > 150 ? '151-200' : hoveredCell.hotspotCount > 100 ? '101-150' : hoveredCell.hotspotCount > 50 ? '51-100' : hoveredCell.hotspotCount > 10 ? '11-50' : '0-10'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-6 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"将鼠标悬停在网格上\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: \"查看详细信息\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"总热点数\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.formatCount)(totalHotspots)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"可见热点\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.formatCount)(visibleHotspots)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-3 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500 space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"• 网格大小: 0.1\\xb0 \\xd7 0.1\\xb0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"• 数据更新: 实时\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"• 点击网格可查看详情\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_c = InfoPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InfoPanel);\nvar _c;\n$RefreshReg$(_c, \"InfoPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/info-panel.tsx\n"));

/***/ })

});