"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-hotspots/page",{

/***/ "(app-pages-browser)/./app/test-hotspots/page.tsx":
/*!************************************!*\
  !*** ./app/test-hotspots/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestHotspotsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_hotspot_legend__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/hotspot-legend */ \"(app-pages-browser)/./components/hotspot-legend.tsx\");\n/* harmony import */ var _components_map_controls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/map-controls */ \"(app-pages-browser)/./components/map-controls.tsx\");\n/* harmony import */ var _components_info_panel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/info-panel */ \"(app-pages-browser)/./components/info-panel.tsx\");\n/* harmony import */ var _components_search_bar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/search-bar */ \"(app-pages-browser)/./components/search-bar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TestHotspotsPage() {\n    _s();\n    const [layerType, setLayerType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('street');\n    const [hoveredCell, setHoveredCell] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 模拟数据\n    const mockCell = {\n        id: 'test-cell',\n        bounds: {\n            north: 40.0,\n            south: 39.9,\n            east: 116.5,\n            west: 116.4\n        },\n        center: {\n            lat: 39.95,\n            lng: 116.45\n        },\n        hotspotCount: 125,\n        color: '#FD8D3C'\n    };\n    const handleLocationSelect = (location)=>{\n        console.log('Selected location:', location);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold text-gray-900 mb-6\",\n                    children: \"eBird 热点地图测试\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_search_bar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                onLocationSelect: handleLocationSelect,\n                                placeholder: \"搜索地点...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-8 h-96 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl mb-4\",\n                                            children: \"\\uD83D\\uDDFA️\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                            children: \"地图区域\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"这里将显示交互式热点地图\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-8 gap-1\",\n                                            children: Array.from({\n                                                length: 64\n                                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded-sm\",\n                                                    style: {\n                                                        backgroundColor: i % 8 === 0 ? '#800026' : i % 7 === 0 ? '#BD0026' : i % 6 === 0 ? '#E31A1C' : i % 5 === 0 ? '#FC4E2A' : i % 4 === 0 ? '#FD8D3C' : i % 3 === 0 ? '#FEB24C' : i % 2 === 0 ? '#FED976' : '#FFEDA0'\n                                                    }\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_info_panel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    hoveredCell: hoveredCell,\n                                    totalHotspots: 12847,\n                                    visibleHotspots: 3421,\n                                    currentZoom: 8\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hotspot_legend__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_controls__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    currentLayer: layerType,\n                                    onLayerChange: setLayerType\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-700 mb-3\",\n                                            children: \"测试控件\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setHoveredCell(mockCell),\n                                                    className: \"w-full px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600\",\n                                                    children: \"模拟悬停网格\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setHoveredCell(null),\n                                                    className: \"w-full px-3 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600\",\n                                                    children: \"清除悬停\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"eBird 热点地图功能\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83C\\uDFAF 网格化热点显示\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"地图被分割成规则网格，每个网格根据包含的热点数量显示不同颜色\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83C\\uDFA8 颜色分级系统\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"从浅黄色到深红色的8级颜色分级，直观显示热点密度\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83D\\uDD0D 交互式搜索\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"支持地点名称搜索，快速定位到感兴趣的区域\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83D\\uDDFA️ 多种地图类型\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"支持街道图、卫星图、地形图和混合图等多种底图\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83D\\uDCCA 实时信息显示\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"鼠标悬停时显示网格详细信息，包括坐标和热点数量\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83D\\uDCF1 响应式设计\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"适配不同设备屏幕，提供一致的用户体验\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_s(TestHotspotsPage, \"lP7rQNDmTov8553k65gQD1QmQR0=\");\n_c = TestHotspotsPage;\nvar _c;\n$RefreshReg$(_c, \"TestHotspotsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-hotspots/page.tsx\n"));

/***/ })

});