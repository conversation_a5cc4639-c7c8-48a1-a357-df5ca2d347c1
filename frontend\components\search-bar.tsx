'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Search, MapPin, X } from 'lucide-react';
import { SearchResult } from '@/lib/hotspot-types';

interface SearchBarProps {
  onLocationSelect: (location: { lat: number; lng: number; name: string }) => void;
  placeholder?: string;
  className?: string;
}

// 模拟搜索结果
const mockSearchResults: SearchResult[] = [
  { id: '1', name: '北京市', lat: 39.9042, lng: 116.4074, type: 'city' },
  { id: '2', name: '上海市', lat: 31.2304, lng: 121.4737, type: 'city' },
  { id: '3', name: '广州市', lat: 23.1291, lng: 113.2644, type: 'city' },
  { id: '4', name: '深圳市', lat: 22.5431, lng: 114.0579, type: 'city' },
  { id: '5', name: '杭州市', lat: 30.2741, lng: 120.1551, type: 'city' },
  { id: '6', name: '南京市', lat: 32.0603, lng: 118.7969, type: 'city' },
  { id: '7', name: '成都市', lat: 30.5728, lng: 104.0668, type: 'city' },
  { id: '8', name: '西安市', lat: 34.3416, lng: 108.9398, type: 'city' },
];

export function SearchBar({ onLocationSelect, placeholder = '输入地点名称或坐标...', className = '' }: SearchBarProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 搜索函数
  const search = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    // 模拟搜索延迟
    const filteredResults = mockSearchResults.filter(result =>
      result.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    
    setResults(filteredResults);
  }, []);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    search(value);
    setIsOpen(true);
    setSelectedIndex(-1);
  };

  // 处理选择结果
  const handleSelectResult = (result: SearchResult) => {
    setQuery(result.name);
    setIsOpen(false);
    setResults([]);
    onLocationSelect({
      lat: result.lat,
      lng: result.lng,
      name: result.name,
    });
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handleSelectResult(results[selectedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        break;
    }
  };

  // 清除搜索
  const clearSearch = () => {
    setQuery('');
    setResults([]);
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => query && setIsOpen(true)}
          placeholder={placeholder}
          className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg 
                   focus:ring-2 focus:ring-blue-500 focus:border-transparent
                   bg-white text-sm placeholder-gray-500"
        />
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>

      {/* 搜索结果下拉框 */}
      {isOpen && results.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
          {results.map((result, index) => (
            <button
              key={result.id}
              onClick={() => handleSelectResult(result)}
              className={`w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3
                        ${index === selectedIndex ? 'bg-blue-50 text-blue-700' : 'text-gray-700'}
                        ${index === 0 ? 'rounded-t-lg' : ''}
                        ${index === results.length - 1 ? 'rounded-b-lg' : 'border-b border-gray-100'}`}
            >
              <MapPin className="h-4 w-4 text-gray-400" />
              <div>
                <div className="font-medium">{result.name}</div>
                <div className="text-xs text-gray-500">
                  {result.lat.toFixed(4)}, {result.lng.toFixed(4)}
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

export default SearchBar;
