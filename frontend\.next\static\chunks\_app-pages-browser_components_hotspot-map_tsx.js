"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_components_hotspot-map_tsx"],{

/***/ "(app-pages-browser)/./components/hotspot-map.tsx":
/*!************************************!*\
  !*** ./components/hotspot-map.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HotspotMap: () => (/* binding */ HotspotMap),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hotspot-utils */ \"(app-pages-browser)/./lib/hotspot-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ HotspotMap,default auto */ \nvar _s = $RefreshSig$();\n\n\n// 地图图层配置\nconst LAYER_CONFIGS = {\n    street: {\n        url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',\n        attribution: '© OpenStreetMap contributors'\n    },\n    satellite: {\n        url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',\n        attribution: '© Esri'\n    },\n    terrain: {\n        url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',\n        attribution: '© OpenTopoMap contributors'\n    },\n    hybrid: {\n        url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',\n        attribution: '© Esri'\n    }\n};\nfunction HotspotMap(param) {\n    let { center, zoom, layerType, onMapMove, onGridCellHover, className = '' } = param;\n    _s();\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mapInstanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const gridLayerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [gridCells, setGridCells] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 初始化地图\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HotspotMap.useEffect\": ()=>{\n            if (!mapRef.current || mapInstanceRef.current) return;\n            // 动态导入 Leaflet\n            __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_leaflet_1_9_4_node_modules_leaflet_dist_leaflet-src_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! leaflet */ \"(app-pages-browser)/./node_modules/.pnpm/leaflet@1.9.4/node_modules/leaflet/dist/leaflet-src.js\", 23)).then({\n                \"HotspotMap.useEffect\": (L)=>{\n                    // 修复 Leaflet 图标问题\n                    delete L.Icon.Default.prototype._getIconUrl;\n                    L.Icon.Default.mergeOptions({\n                        iconRetinaUrl: '/leaflet/marker-icon-2x.png',\n                        iconUrl: '/leaflet/marker-icon.png',\n                        shadowUrl: '/leaflet/marker-shadow.png'\n                    });\n                    // 创建地图实例\n                    const map = L.map(mapRef.current, {\n                        center: center,\n                        zoom: zoom,\n                        zoomControl: false,\n                        attributionControl: false\n                    });\n                    // 添加缩放控件到右下角\n                    L.control.zoom({\n                        position: 'bottomright'\n                    }).addTo(map);\n                    // 添加基础图层\n                    const tileLayer = L.tileLayer(LAYER_CONFIGS[layerType].url, {\n                        attribution: LAYER_CONFIGS[layerType].attribution,\n                        maxZoom: 18\n                    }).addTo(map);\n                    mapInstanceRef.current = map;\n                    setIsLoading(false);\n                    // 监听地图移动事件\n                    map.on('moveend', {\n                        \"HotspotMap.useEffect\": ()=>{\n                            const center = map.getCenter();\n                            const zoom = map.getZoom();\n                            onMapMove === null || onMapMove === void 0 ? void 0 : onMapMove([\n                                center.lat,\n                                center.lng\n                            ], zoom);\n                            updateGrid(map, L);\n                        }\n                    }[\"HotspotMap.useEffect\"]);\n                    // 初始化网格\n                    updateGrid(map, L);\n                }\n            }[\"HotspotMap.useEffect\"]);\n            return ({\n                \"HotspotMap.useEffect\": ()=>{\n                    if (mapInstanceRef.current) {\n                        mapInstanceRef.current.remove();\n                        mapInstanceRef.current = null;\n                    }\n                }\n            })[\"HotspotMap.useEffect\"];\n        }\n    }[\"HotspotMap.useEffect\"], []);\n    // 更新网格\n    const updateGrid = (map, L)=>{\n        if (!map) return;\n        // 移除现有网格图层\n        if (gridLayerRef.current) {\n            map.removeLayer(gridLayerRef.current);\n        }\n        // 获取当前地图边界\n        const bounds = map.getBounds();\n        const mapBounds = {\n            north: bounds.getNorth(),\n            south: bounds.getSouth(),\n            east: bounds.getEast(),\n            west: bounds.getWest()\n        };\n        // 根据缩放级别获取合适的网格大小\n        const currentZoom = map.getZoom();\n        const gridSize = (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.getGridSizeByZoom)(currentZoom);\n        const cells = (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.generateGrid)(mapBounds, gridSize);\n        // 根据缩放级别和网格大小调整热点数据密度\n        const hotspotDensity = Math.max(100, Math.min(2000, Math.floor(1000 / gridSize)));\n        const hotspots = (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.generateMockHotspots)(mapBounds, hotspotDensity);\n        // 将热点分配到网格\n        const updatedCells = (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.assignHotspotsToGrid)(cells, hotspots);\n        setGridCells(updatedCells);\n        // 创建网格图层组\n        const gridLayerGroup = L.layerGroup();\n        // 添加网格矩形\n        updatedCells.forEach((cell)=>{\n            if (cell.hotspotCount === 0) return; // 不显示空网格\n            const rectangle = L.rectangle([\n                [\n                    cell.bounds.south,\n                    cell.bounds.west\n                ],\n                [\n                    cell.bounds.north,\n                    cell.bounds.east\n                ]\n            ], {\n                fillColor: cell.color,\n                fillOpacity: 0.6,\n                color: cell.color,\n                weight: 1,\n                opacity: 0.8\n            });\n            // 添加鼠标事件\n            rectangle.on('mouseover', ()=>{\n                rectangle.setStyle({\n                    fillOpacity: 0.8,\n                    weight: 2\n                });\n                onGridCellHover === null || onGridCellHover === void 0 ? void 0 : onGridCellHover(cell);\n            });\n            rectangle.on('mouseout', ()=>{\n                rectangle.setStyle({\n                    fillOpacity: 0.6,\n                    weight: 1\n                });\n                onGridCellHover === null || onGridCellHover === void 0 ? void 0 : onGridCellHover(null);\n            });\n            rectangle.addTo(gridLayerGroup);\n        });\n        gridLayerGroup.addTo(map);\n        gridLayerRef.current = gridLayerGroup;\n    };\n    // 更新图层类型\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HotspotMap.useEffect\": ()=>{\n            if (!mapInstanceRef.current) return;\n            __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_leaflet_1_9_4_node_modules_leaflet_dist_leaflet-src_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! leaflet */ \"(app-pages-browser)/./node_modules/.pnpm/leaflet@1.9.4/node_modules/leaflet/dist/leaflet-src.js\", 23)).then({\n                \"HotspotMap.useEffect\": (L)=>{\n                    const map = mapInstanceRef.current;\n                    // 移除所有图层\n                    map.eachLayer({\n                        \"HotspotMap.useEffect\": (layer)=>{\n                            if (layer instanceof L.TileLayer) {\n                                map.removeLayer(layer);\n                            }\n                        }\n                    }[\"HotspotMap.useEffect\"]);\n                    // 添加新图层\n                    L.tileLayer(LAYER_CONFIGS[layerType].url, {\n                        attribution: LAYER_CONFIGS[layerType].attribution,\n                        maxZoom: 18\n                    }).addTo(map);\n                }\n            }[\"HotspotMap.useEffect\"]);\n        }\n    }[\"HotspotMap.useEffect\"], [\n        layerType\n    ]);\n    // 更新地图中心和缩放\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HotspotMap.useEffect\": ()=>{\n            if (!mapInstanceRef.current) return;\n            const map = mapInstanceRef.current;\n            const currentCenter = map.getCenter();\n            const currentZoom = map.getZoom();\n            if (Math.abs(currentCenter.lat - center[0]) > 0.001 || Math.abs(currentCenter.lng - center[1]) > 0.001 || currentZoom !== zoom) {\n                map.setView(center, zoom);\n            }\n        }\n    }[\"HotspotMap.useEffect\"], [\n        center,\n        zoom\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: mapRef,\n                className: \"w-full h-full rounded-lg overflow-hidden\",\n                style: {\n                    minHeight: '400px'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-map.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-map.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"加载地图中...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-map.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-map.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-map.tsx\",\n                lineNumber: 217,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-map.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_s(HotspotMap, \"6mVjALYJXATweClLKSBk72LEdEE=\");\n_c = HotspotMap;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HotspotMap);\nvar _c;\n$RefreshReg$(_c, \"HotspotMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/hotspot-map.tsx\n"));

/***/ })

}]);