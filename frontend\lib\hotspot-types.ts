// 热点数据类型定义
export interface HotspotData {
  id: string;
  lat: number;
  lng: number;
  count: number;
}

// 网格单元类型
export interface GridCell {
  id: string;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  center: {
    lat: number;
    lng: number;
  };
  hotspotCount: number;
  color: string;
}

// 地图边界类型
export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

// 颜色分级配置
export interface ColorGrade {
  min: number;
  max: number;
  color: string;
  label: string;
}

// 搜索结果类型
export interface SearchResult {
  id: string;
  name: string;
  lat: number;
  lng: number;
  type: string;
}

// 地图图层类型
export type MapLayerType = 'street' | 'satellite' | 'terrain' | 'hybrid';

// 地图配置
export interface MapConfig {
  center: [number, number];
  zoom: number;
  minZoom: number;
  maxZoom: number;
  gridSize: number; // 网格大小（度）
}
