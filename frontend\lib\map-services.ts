/**
 * 地图服务配置
 * 统一管理不同地图服务的配置信息
 */

export interface MapServiceConfig {
  id: string
  name: string
  description: string
  tileUrl: string
  attribution: string
  subdomains: string[]
  maxZoom: number
  minZoom?: number
  options?: Record<string, any>
  // 新增：区域适用性信息
  bestForRegions?: string[] // 最适合的地理区域ID列表
  globalCoverage?: boolean // 是否有良好的全球覆盖
  detailLevel?: 'high' | 'medium' | 'low' // 详细程度
}

/**
 * 地图服务配置列表
 * 仅保留高德地图作为唯一地图服务
 */
export const mapServices: MapServiceConfig[] = [
  {
    id: "amap",
    name: "高德地图",
    description: "中国大陆优化，快速稳定",
    tileUrl: "https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}",
    attribution: "© 高德地图 AutoNavi",
    subdomains: ["1", "2", "3", "4"],
    maxZoom: 18,
    minZoom: 3,
    bestForRegions: ["china-mainland"],
    globalCoverage: false,
    detailLevel: "high",
    options: {
      tileSize: 256,
      zoomOffset: 0,
      keepBuffer: 2,
      updateWhenIdle: false,
      updateWhenZooming: true,
      crossOrigin: true,
    }
  }
]

/**
 * 获取地图服务配置
 */
export function getMapServiceConfig(serviceId: string): MapServiceConfig | undefined {
  return mapServices.find(service => service.id === serviceId)
}

/**
 * 获取默认地图服务
 */
export function getDefaultMapService(): MapServiceConfig {
  return mapServices[0] // 高德地图作为唯一服务
}

/**
 * 获取地图服务选择器选项
 */
export function getMapServiceOptions() {
  return mapServices.map(service => ({
    value: service.id,
    label: service.name,
    description: service.description,
  }))
}
