'use client';

import React, { useEffect, useRef, useState } from 'react';
import { MapLayerType, GridCell, HotspotData, MapBounds } from '@/lib/hotspot-types';
import { generateGrid, generateMockHotspots, assignHotspotsToGrid, calculateBounds, getGridSizeByZoom } from '@/lib/hotspot-utils';

interface HotspotMapProps {
  center: [number, number];
  zoom: number;
  layerType: MapLayerType;
  onMapMove?: (center: [number, number], zoom: number) => void;
  onGridCellHover?: (cell: GridCell | null) => void;
  className?: string;
}

// 地图图层配置
const LAYER_CONFIGS = {
  street: {
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© OpenStreetMap contributors',
  },
  satellite: {
    url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    attribution: '© Esri',
  },
  terrain: {
    url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',
    attribution: '© OpenTopoMap contributors',
  },
  hybrid: {
    url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    attribution: '© Esri',
  },
};

export function HotspotMap({ 
  center, 
  zoom, 
  layerType, 
  onMapMove, 
  onGridCellHover,
  className = '' 
}: HotspotMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const gridLayerRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [gridCells, setGridCells] = useState<GridCell[]>([]);

  // 初始化地图
  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    // 动态导入 Leaflet
    import('leaflet').then((L) => {
      // 修复 Leaflet 图标问题
      delete (L.Icon.Default.prototype as any)._getIconUrl;
      L.Icon.Default.mergeOptions({
        iconRetinaUrl: '/leaflet/marker-icon-2x.png',
        iconUrl: '/leaflet/marker-icon.png',
        shadowUrl: '/leaflet/marker-shadow.png',
      });

      // 创建地图实例
      const map = L.map(mapRef.current!, {
        center: center,
        zoom: zoom,
        zoomControl: false,
        attributionControl: false,
      });

      // 添加缩放控件到右下角
      L.control.zoom({ position: 'bottomright' }).addTo(map);

      // 添加基础图层
      const tileLayer = L.tileLayer(LAYER_CONFIGS[layerType].url, {
        attribution: LAYER_CONFIGS[layerType].attribution,
        maxZoom: 18,
      }).addTo(map);

      mapInstanceRef.current = map;
      setIsLoading(false);

      // 监听地图移动事件
      map.on('moveend', () => {
        const center = map.getCenter();
        const zoom = map.getZoom();
        onMapMove?.([center.lat, center.lng], zoom);
        updateGrid(map, L);
      });

      // 初始化网格
      updateGrid(map, L);
    });

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []);

  // 更新网格
  const updateGrid = (map: any, L: any) => {
    if (!map) return;

    // 移除现有网格图层
    if (gridLayerRef.current) {
      map.removeLayer(gridLayerRef.current);
    }

    // 获取当前地图边界
    const bounds = map.getBounds();
    const mapBounds: MapBounds = {
      north: bounds.getNorth(),
      south: bounds.getSouth(),
      east: bounds.getEast(),
      west: bounds.getWest(),
    };

    // 根据缩放级别获取合适的网格大小
    const currentZoom = map.getZoom();
    const gridSize = getGridSizeByZoom(currentZoom);
    const cells = generateGrid(mapBounds, gridSize);

    // 根据缩放级别和网格大小调整热点数据密度
    const hotspotDensity = Math.max(100, Math.min(2000, Math.floor(1000 / gridSize)));
    const hotspots = generateMockHotspots(mapBounds, hotspotDensity);
    
    // 将热点分配到网格
    const updatedCells = assignHotspotsToGrid(cells, hotspots);
    setGridCells(updatedCells);

    // 创建网格图层组
    const gridLayerGroup = L.layerGroup();

    // 添加网格矩形
    updatedCells.forEach((cell) => {
      if (cell.hotspotCount === 0) return; // 不显示空网格

      const rectangle = L.rectangle(
        [[cell.bounds.south, cell.bounds.west], [cell.bounds.north, cell.bounds.east]],
        {
          fillColor: cell.color,
          fillOpacity: 0.6,
          color: cell.color,
          weight: 1,
          opacity: 0.8,
        }
      );

      // 添加鼠标事件
      rectangle.on('mouseover', () => {
        rectangle.setStyle({ fillOpacity: 0.8, weight: 2 });
        onGridCellHover?.(cell);
      });

      rectangle.on('mouseout', () => {
        rectangle.setStyle({ fillOpacity: 0.6, weight: 1 });
        onGridCellHover?.(null);
      });

      rectangle.addTo(gridLayerGroup);
    });

    gridLayerGroup.addTo(map);
    gridLayerRef.current = gridLayerGroup;
  };

  // 更新图层类型
  useEffect(() => {
    if (!mapInstanceRef.current) return;

    import('leaflet').then((L) => {
      const map = mapInstanceRef.current;
      
      // 移除所有图层
      map.eachLayer((layer: any) => {
        if (layer instanceof L.TileLayer) {
          map.removeLayer(layer);
        }
      });

      // 添加新图层
      L.tileLayer(LAYER_CONFIGS[layerType].url, {
        attribution: LAYER_CONFIGS[layerType].attribution,
        maxZoom: 18,
      }).addTo(map);
    });
  }, [layerType]);

  // 更新地图中心和缩放
  useEffect(() => {
    if (!mapInstanceRef.current) return;
    
    const map = mapInstanceRef.current;
    const currentCenter = map.getCenter();
    const currentZoom = map.getZoom();
    
    if (Math.abs(currentCenter.lat - center[0]) > 0.001 || 
        Math.abs(currentCenter.lng - center[1]) > 0.001 || 
        currentZoom !== zoom) {
      map.setView(center, zoom);
    }
  }, [center, zoom]);

  return (
    <div className={`relative ${className}`}>
      <div 
        ref={mapRef} 
        className="w-full h-full rounded-lg overflow-hidden"
        style={{ minHeight: '400px' }}
      />
      
      {isLoading && (
        <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="flex items-center space-x-2 text-gray-600">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span>加载地图中...</span>
          </div>
        </div>
      )}
    </div>
  );
}

export default HotspotMap;
