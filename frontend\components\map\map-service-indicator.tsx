"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip"
import { Map, Settings, Info, RefreshCw } from "lucide-react"
import { getMapServiceConfig } from "@/lib/map-services"

interface MapServiceIndicatorProps {
  currentService: string
  currentRegion: string | null
  autoSwitchEnabled: boolean
  onServiceChange: (serviceId: string) => void
  onAutoSwitchToggle: (enabled: boolean) => void
  availableServices: string[]
  className?: string
}

export default function MapServiceIndicator({
  currentService,
  currentRegion,
  autoSwitchEnabled,
  onServiceChange,
  onAutoSwitchToggle,
  availableServices,
  className = ""
}: MapServiceIndicatorProps) {
  const [showDetails, setShowDetails] = useState(false)
  const currentServiceConfig = getMapServiceConfig(currentService)

  const regionNames: Record<string, string> = {
    "china-mainland": "中国大陆"
  }

  const getRegionName = (regionId: string | null): string => {
    if (!regionId) return "全球"
    return regionNames[regionId] || regionId
  }

  const getServiceBadgeColor = (serviceId: string): string => {
    switch (serviceId) {
      case "amap": return "bg-blue-500"
      default: return "bg-blue-500"
    }
  }

  return (
    <div className={`fixed bottom-4 right-4 z-[1000] ${className}`}>
      <Card className="bg-white/95 backdrop-blur-sm shadow-lg border">
        <CardContent className="p-3">
          <div className="flex items-center gap-2">
            {/* 地图服务指示器 */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-2">
                    <Map className="w-4 h-4 text-gray-600" />
                    <Badge 
                      className={`${getServiceBadgeColor(currentService)} text-white text-xs`}
                    >
                      {currentServiceConfig?.name || currentService}
                    </Badge>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-sm">
                    <div className="font-medium">当前地图服务</div>
                    <div className="text-gray-600">
                      {currentServiceConfig?.description}
                    </div>
                    <div className="text-gray-600 mt-1">
                      区域: {getRegionName(currentRegion)}
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* 自动切换状态 */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge 
                    variant={autoSwitchEnabled ? "default" : "secondary"}
                    className="text-xs cursor-pointer"
                    onClick={() => onAutoSwitchToggle(!autoSwitchEnabled)}
                  >
                    <RefreshCw className={`w-3 h-3 mr-1 ${autoSwitchEnabled ? 'animate-spin' : ''}`} />
                    {autoSwitchEnabled ? "智能" : "手动"}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-sm">
                    {autoSwitchEnabled 
                      ? "智能切换已启用，将根据地理区域自动选择最佳地图服务" 
                      : "智能切换已禁用，点击切换到智能模式"
                    }
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* 设置按钮（仅显示） */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0" disabled>
                    <Settings className="w-3 h-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-sm">
                    当前仅支持高德地图服务
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
