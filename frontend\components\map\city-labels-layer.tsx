/**
 * 城市标注层组件
 * 在地图上显示海外城市的中文标注
 */

import { useEffect, useRef } from "react"
import L from "leaflet"
import { OverseasCity, getCitiesByZoomLevel } from "@/data/overseas-cities"

interface CityLabelsLayerProps {
  map: L.Map | null
  currentRegion: string | null
  zoomLevel: number
  visible: boolean
  onCityClick?: (city: OverseasCity) => void
}

export default function CityLabelsLayer({
  map,
  currentRegion,
  zoomLevel,
  visible,
  onCityClick
}: CityLabelsLayerProps) {
  const layerRef = useRef<L.LayerGroup | null>(null)
  const markersRef = useRef<Map<string, L.Marker>>(new Map())

  // 创建城市标注样式
  const createCityIcon = (city: OverseasCity, zoom: number): L.DivIcon => {
    const isCapital = city.isCapital
    const isLargeCity = (city.population || 0) > 5000000
    const isMediumCity = (city.population || 0) > 2000000

    // 根据城市重要性和缩放级别确定样式
    let fontSize = '12px'
    let fontWeight = 'normal'
    let color = '#333'
    let backgroundColor = 'rgba(255, 255, 255, 0.8)'
    let padding = '2px 6px'
    let borderRadius = '3px'
    let border = '1px solid #ccc'

    if (isCapital) {
      fontSize = zoom >= 6 ? '14px' : '13px'
      fontWeight = 'bold'
      color = '#d32f2f'
      backgroundColor = 'rgba(255, 255, 255, 0.95)'
      border = '2px solid #d32f2f'
    } else if (isLargeCity) {
      fontSize = zoom >= 7 ? '13px' : '12px'
      fontWeight = '600'
      color = '#1976d2'
      backgroundColor = 'rgba(255, 255, 255, 0.9)'
      border = '1px solid #1976d2'
    } else if (isMediumCity) {
      fontSize = '12px'
      fontWeight = '500'
      color = '#388e3c'
    }

    // 根据缩放级别调整显示
    if (zoom < 5) {
      fontSize = '11px'
      padding = '1px 4px'
    } else if (zoom >= 8) {
      fontSize = zoom >= 10 ? '15px' : '14px'
      padding = '3px 8px'
    }

    const html = `
      <div style="
        font-size: ${fontSize};
        font-weight: ${fontWeight};
        color: ${color};
        background-color: ${backgroundColor};
        padding: ${padding};
        border-radius: ${borderRadius};
        border: ${border};
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        white-space: nowrap;
        cursor: pointer;
        user-select: none;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.2;
        transition: all 0.2s ease;
      " 
      onmouseover="this.style.transform='scale(1.1)'; this.style.zIndex='1000';"
      onmouseout="this.style.transform='scale(1)'; this.style.zIndex='auto';"
      >
        ${city.name}
      </div>
    `

    return L.divIcon({
      html,
      className: 'city-label-marker',
      iconSize: [0, 0],
      iconAnchor: [0, 0],
    })
  }

  // 更新城市标注
  const updateCityLabels = () => {
    if (!map || !layerRef.current || !visible) return

    // 清除现有标注
    markersRef.current.forEach(marker => {
      layerRef.current?.removeLayer(marker)
    })
    markersRef.current.clear()

    // 如果没有当前区域，不显示标注
    if (!currentRegion) return

    // 获取当前区域和缩放级别应该显示的城市
    const citiesToShow = getCitiesByZoomLevel(currentRegion, zoomLevel)

    // 创建新的城市标注
    citiesToShow.forEach(city => {
      const icon = createCityIcon(city, zoomLevel)
      const marker = L.marker([city.lat, city.lng], { 
        icon,
        zIndexOffset: city.isCapital ? 1000 : (city.population || 0) > 5000000 ? 500 : 100
      })

      // 添加点击事件
      marker.on('click', () => {
        if (onCityClick) {
          onCityClick(city)
        }
      })

      // 添加悬停提示
      const popupContent = `
        <div class="city-popup">
          <h4 class="font-semibold text-lg mb-2">${city.name}</h4>
          <div class="space-y-1 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">英文名:</span>
              <span class="font-medium">${city.nameEn}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">国家:</span>
              <span class="font-medium">${city.country}</span>
            </div>
            ${city.population ? `
              <div class="flex justify-between">
                <span class="text-gray-600">人口:</span>
                <span class="font-medium">${(city.population / 10000).toFixed(0)}万</span>
              </div>
            ` : ''}
            ${city.isCapital ? `
              <div class="mt-2">
                <span class="inline-block px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
                  首都
                </span>
              </div>
            ` : ''}
          </div>
        </div>
      `

      marker.bindPopup(popupContent, {
        offset: [0, -10],
        className: 'city-label-popup'
      })

      layerRef.current?.addLayer(marker)
      markersRef.current.set(city.id, marker)
    })
  }

  // 初始化图层
  useEffect(() => {
    if (!map) return

    // 创建城市标注图层
    layerRef.current = L.layerGroup()
    
    if (visible) {
      layerRef.current.addTo(map)
    }

    return () => {
      if (layerRef.current) {
        map.removeLayer(layerRef.current)
        layerRef.current = null
      }
      markersRef.current.clear()
    }
  }, [map])

  // 监听可见性变化
  useEffect(() => {
    if (!map || !layerRef.current) return

    if (visible) {
      if (!map.hasLayer(layerRef.current)) {
        layerRef.current.addTo(map)
      }
      updateCityLabels()
    } else {
      if (map.hasLayer(layerRef.current)) {
        map.removeLayer(layerRef.current)
      }
    }
  }, [visible])

  // 监听区域、缩放级别变化
  useEffect(() => {
    if (visible) {
      updateCityLabels()
    }
  }, [currentRegion, zoomLevel, visible])

  return null // 这是一个纯逻辑组件，不渲染任何UI
}

// 导出城市标注层的CSS样式
export const cityLabelsStyles = `
  .city-label-marker {
    background: transparent !important;
    border: none !important;
  }

  .city-label-popup .leaflet-popup-content-wrapper {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .city-label-popup .leaflet-popup-content {
    margin: 12px;
    line-height: 1.4;
  }

  .city-popup h4 {
    margin: 0 0 8px 0;
    color: #1f2937;
  }

  .city-popup .space-y-1 > * + * {
    margin-top: 4px;
  }

  .leaflet-popup-close-button {
    color: #6b7280 !important;
    font-size: 18px !important;
    padding: 4px 8px !important;
  }

  .leaflet-popup-close-button:hover {
    color: #374151 !important;
  }
`
