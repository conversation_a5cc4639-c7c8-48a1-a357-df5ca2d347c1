"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hotspots/page",{

/***/ "(app-pages-browser)/./lib/hotspot-utils.ts":
/*!******************************!*\
  !*** ./lib/hotspot-utils.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLOR_GRADES: () => (/* binding */ COLOR_GRADES),\n/* harmony export */   assignHotspotsToGrid: () => (/* binding */ assignHotspotsToGrid),\n/* harmony export */   calculateBounds: () => (/* binding */ calculateBounds),\n/* harmony export */   formatCount: () => (/* binding */ formatCount),\n/* harmony export */   generateGrid: () => (/* binding */ generateGrid),\n/* harmony export */   generateMockHotspots: () => (/* binding */ generateMockHotspots),\n/* harmony export */   getColorByCount: () => (/* binding */ getColorByCount),\n/* harmony export */   getGridSizeByZoom: () => (/* binding */ getGridSizeByZoom)\n/* harmony export */ });\n// eBird 风格的颜色分级配置\nconst COLOR_GRADES = [\n    {\n        min: 0,\n        max: 10,\n        color: '#FFEDA0',\n        label: '0-10'\n    },\n    {\n        min: 11,\n        max: 50,\n        color: '#FED976',\n        label: '11-50'\n    },\n    {\n        min: 51,\n        max: 100,\n        color: '#FEB24C',\n        label: '51-100'\n    },\n    {\n        min: 101,\n        max: 150,\n        color: '#FD8D3C',\n        label: '101-150'\n    },\n    {\n        min: 151,\n        max: 200,\n        color: '#FC4E2A',\n        label: '151-200'\n    },\n    {\n        min: 201,\n        max: 300,\n        color: '#E31A1C',\n        label: '201-300'\n    },\n    {\n        min: 301,\n        max: 500,\n        color: '#BD0026',\n        label: '301-500'\n    },\n    {\n        min: 501,\n        max: Infinity,\n        color: '#800026',\n        label: '500+'\n    }\n];\n// 根据热点数量获取颜色\nfunction getColorByCount(count) {\n    const grade = COLOR_GRADES.find((g)=>count >= g.min && count <= g.max);\n    return (grade === null || grade === void 0 ? void 0 : grade.color) || COLOR_GRADES[0].color;\n}\n// 生成网格\nfunction generateGrid(bounds, gridSize) {\n    const cells = [];\n    // 计算网格数量\n    const latSteps = Math.ceil((bounds.north - bounds.south) / gridSize);\n    const lngSteps = Math.ceil((bounds.east - bounds.west) / gridSize);\n    // 限制最大网格数量以提高性能\n    const maxCells = 10000; // 最多10000个网格\n    const totalCells = latSteps * lngSteps;\n    if (totalCells > maxCells) {\n        // 如果网格太多，增加网格大小\n        const scaleFactor = Math.sqrt(totalCells / maxCells);\n        const adjustedGridSize = gridSize * scaleFactor;\n        return generateGrid(bounds, adjustedGridSize);\n    }\n    for(let i = 0; i < latSteps; i++){\n        for(let j = 0; j < lngSteps; j++){\n            const south = bounds.south + i * gridSize;\n            const north = Math.min(bounds.north, south + gridSize);\n            const west = bounds.west + j * gridSize;\n            const east = Math.min(bounds.east, west + gridSize);\n            const cell = {\n                id: \"cell-\".concat(i, \"-\").concat(j),\n                bounds: {\n                    north,\n                    south,\n                    east,\n                    west\n                },\n                center: {\n                    lat: (north + south) / 2,\n                    lng: (east + west) / 2\n                },\n                hotspotCount: 0,\n                color: COLOR_GRADES[0].color\n            };\n            cells.push(cell);\n        }\n    }\n    return cells;\n}\n// 将热点数据分配到网格中\nfunction assignHotspotsToGrid(cells, hotspots) {\n    // 重置所有网格的热点数量\n    cells.forEach((cell)=>{\n        cell.hotspotCount = 0;\n    });\n    // 将热点分配到对应的网格\n    hotspots.forEach((hotspot)=>{\n        const cell = cells.find((c)=>hotspot.lat >= c.bounds.south && hotspot.lat < c.bounds.north && hotspot.lng >= c.bounds.west && hotspot.lng < c.bounds.east);\n        if (cell) {\n            cell.hotspotCount += hotspot.count;\n        }\n    });\n    // 更新网格颜色\n    cells.forEach((cell)=>{\n        cell.color = getColorByCount(cell.hotspotCount);\n    });\n    return cells;\n}\n// 生成模拟热点数据\nfunction generateMockHotspots(bounds, count) {\n    const hotspots = [];\n    // 创建一些热点聚集区域\n    const hotspotClusters = Math.min(10, Math.max(3, Math.floor(count / 100)));\n    const clusterCenters = [];\n    // 生成聚集中心点\n    for(let i = 0; i < hotspotClusters; i++){\n        clusterCenters.push({\n            lat: bounds.south + Math.random() * (bounds.north - bounds.south),\n            lng: bounds.west + Math.random() * (bounds.east - bounds.west)\n        });\n    }\n    for(let i = 0; i < count; i++){\n        let lat, lng;\n        // 70% 的热点围绕聚集中心生成，30% 随机分布\n        if (Math.random() < 0.7 && clusterCenters.length > 0) {\n            const cluster = clusterCenters[Math.floor(Math.random() * clusterCenters.length)];\n            const radius = 0.05; // 聚集半径\n            const angle = Math.random() * 2 * Math.PI;\n            const distance = Math.random() * radius;\n            lat = cluster.lat + Math.cos(angle) * distance;\n            lng = cluster.lng + Math.sin(angle) * distance;\n            // 确保在边界内\n            lat = Math.max(bounds.south, Math.min(bounds.north, lat));\n            lng = Math.max(bounds.west, Math.min(bounds.east, lng));\n        } else {\n            // 随机分布\n            lat = bounds.south + Math.random() * (bounds.north - bounds.south);\n            lng = bounds.west + Math.random() * (bounds.east - bounds.west);\n        }\n        // 使用更真实的热点数量分布（大多数热点数量较少）\n        let hotspotCount;\n        const rand = Math.random();\n        if (rand < 0.4) {\n            hotspotCount = Math.floor(Math.random() * 20) + 1; // 1-20\n        } else if (rand < 0.7) {\n            hotspotCount = Math.floor(Math.random() * 50) + 21; // 21-70\n        } else if (rand < 0.9) {\n            hotspotCount = Math.floor(Math.random() * 100) + 71; // 71-170\n        } else {\n            hotspotCount = Math.floor(Math.random() * 400) + 171; // 171-570\n        }\n        hotspots.push({\n            id: \"hotspot-\".concat(i),\n            lat,\n            lng,\n            count: hotspotCount\n        });\n    }\n    return hotspots;\n}\n// 格式化数字显示\nfunction formatCount(count) {\n    if (count >= 1000) {\n        return \"\".concat((count / 1000).toFixed(1), \"k\");\n    }\n    return count.toString();\n}\n// 根据缩放级别计算合适的网格大小\nfunction getGridSizeByZoom(zoom) {\n    // 缩放级别越高，网格越小，显示越精细\n    if (zoom >= 12) return 0.01; // 高缩放：0.01度网格 (~1km)\n    if (zoom >= 10) return 0.02; // 中高缩放：0.02度网格 (~2km)\n    if (zoom >= 8) return 0.05; // 中缩放：0.05度网格 (~5km)\n    if (zoom >= 6) return 0.1; // 中低缩放：0.1度网格 (~10km)\n    if (zoom >= 4) return 0.2; // 低缩放：0.2度网格 (~20km)\n    return 0.5; // 很低缩放：0.5度网格 (~50km)\n}\n// 计算地图边界\nfunction calculateBounds(center, zoom) {\n    // 根据缩放级别计算合适的边界\n    const latRange = 180 / Math.pow(2, zoom - 1);\n    const lngRange = 360 / Math.pow(2, zoom - 1);\n    return {\n        north: center[0] + latRange / 2,\n        south: center[0] - latRange / 2,\n        east: center[1] + lngRange / 2,\n        west: center[1] - lngRange / 2\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/hotspot-utils.ts\n"));

/***/ })

});