"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-hotspots/page",{

/***/ "(app-pages-browser)/./lib/hotspot-utils.ts":
/*!******************************!*\
  !*** ./lib/hotspot-utils.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLOR_GRADES: () => (/* binding */ COLOR_GRADES),\n/* harmony export */   assignHotspotsToGrid: () => (/* binding */ assignHotspotsToGrid),\n/* harmony export */   calculateBounds: () => (/* binding */ calculateBounds),\n/* harmony export */   formatCount: () => (/* binding */ formatCount),\n/* harmony export */   generateGrid: () => (/* binding */ generateGrid),\n/* harmony export */   generateMockHotspots: () => (/* binding */ generateMockHotspots),\n/* harmony export */   getColorByCount: () => (/* binding */ getColorByCount),\n/* harmony export */   getGridSizeByZoom: () => (/* binding */ getGridSizeByZoom)\n/* harmony export */ });\n// eBird 风格的颜色分级配置\nconst COLOR_GRADES = [\n    {\n        min: 0,\n        max: 10,\n        color: '#FFEDA0',\n        label: '0-10'\n    },\n    {\n        min: 11,\n        max: 50,\n        color: '#FED976',\n        label: '11-50'\n    },\n    {\n        min: 51,\n        max: 100,\n        color: '#FEB24C',\n        label: '51-100'\n    },\n    {\n        min: 101,\n        max: 150,\n        color: '#FD8D3C',\n        label: '101-150'\n    },\n    {\n        min: 151,\n        max: 200,\n        color: '#FC4E2A',\n        label: '151-200'\n    },\n    {\n        min: 201,\n        max: 300,\n        color: '#E31A1C',\n        label: '201-300'\n    },\n    {\n        min: 301,\n        max: 500,\n        color: '#BD0026',\n        label: '301-500'\n    },\n    {\n        min: 501,\n        max: Infinity,\n        color: '#800026',\n        label: '500+'\n    }\n];\n// 根据热点数量获取颜色\nfunction getColorByCount(count) {\n    const grade = COLOR_GRADES.find((g)=>count >= g.min && count <= g.max);\n    return (grade === null || grade === void 0 ? void 0 : grade.color) || COLOR_GRADES[0].color;\n}\n// 生成网格\nfunction generateGrid(bounds, gridSize) {\n    const cells = [];\n    // 计算网格数量\n    const latSteps = Math.ceil((bounds.north - bounds.south) / gridSize);\n    const lngSteps = Math.ceil((bounds.east - bounds.west) / gridSize);\n    for(let i = 0; i < latSteps; i++){\n        for(let j = 0; j < lngSteps; j++){\n            const south = bounds.south + i * gridSize;\n            const north = Math.min(bounds.north, south + gridSize);\n            const west = bounds.west + j * gridSize;\n            const east = Math.min(bounds.east, west + gridSize);\n            const cell = {\n                id: \"cell-\".concat(i, \"-\").concat(j),\n                bounds: {\n                    north,\n                    south,\n                    east,\n                    west\n                },\n                center: {\n                    lat: (north + south) / 2,\n                    lng: (east + west) / 2\n                },\n                hotspotCount: 0,\n                color: COLOR_GRADES[0].color\n            };\n            cells.push(cell);\n        }\n    }\n    return cells;\n}\n// 将热点数据分配到网格中\nfunction assignHotspotsToGrid(cells, hotspots) {\n    // 重置所有网格的热点数量\n    cells.forEach((cell)=>{\n        cell.hotspotCount = 0;\n    });\n    // 将热点分配到对应的网格\n    hotspots.forEach((hotspot)=>{\n        const cell = cells.find((c)=>hotspot.lat >= c.bounds.south && hotspot.lat < c.bounds.north && hotspot.lng >= c.bounds.west && hotspot.lng < c.bounds.east);\n        if (cell) {\n            cell.hotspotCount += hotspot.count;\n        }\n    });\n    // 更新网格颜色\n    cells.forEach((cell)=>{\n        cell.color = getColorByCount(cell.hotspotCount);\n    });\n    return cells;\n}\n// 生成模拟热点数据\nfunction generateMockHotspots(bounds, count) {\n    const hotspots = [];\n    for(let i = 0; i < count; i++){\n        const lat = bounds.south + Math.random() * (bounds.north - bounds.south);\n        const lng = bounds.west + Math.random() * (bounds.east - bounds.west);\n        const hotspotCount = Math.floor(Math.random() * 100) + 1;\n        hotspots.push({\n            id: \"hotspot-\".concat(i),\n            lat,\n            lng,\n            count: hotspotCount\n        });\n    }\n    return hotspots;\n}\n// 格式化数字显示\nfunction formatCount(count) {\n    if (count >= 1000) {\n        return \"\".concat((count / 1000).toFixed(1), \"k\");\n    }\n    return count.toString();\n}\n// 根据缩放级别计算合适的网格大小\nfunction getGridSizeByZoom(zoom) {\n    // 缩放级别越高，网格越小，显示越精细\n    if (zoom >= 12) return 0.01; // 高缩放：0.01度网格 (~1km)\n    if (zoom >= 10) return 0.02; // 中高缩放：0.02度网格 (~2km)\n    if (zoom >= 8) return 0.05; // 中缩放：0.05度网格 (~5km)\n    if (zoom >= 6) return 0.1; // 中低缩放：0.1度网格 (~10km)\n    if (zoom >= 4) return 0.2; // 低缩放：0.2度网格 (~20km)\n    return 0.5; // 很低缩放：0.5度网格 (~50km)\n}\n// 计算地图边界\nfunction calculateBounds(center, zoom) {\n    // 根据缩放级别计算合适的边界\n    const latRange = 180 / Math.pow(2, zoom - 1);\n    const lngRange = 360 / Math.pow(2, zoom - 1);\n    return {\n        north: center[0] + latRange / 2,\n        south: center[0] - latRange / 2,\n        east: center[1] + lngRange / 2,\n        west: center[1] - lngRange / 2\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9ob3RzcG90LXV0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRUEsa0JBQWtCO0FBQ1gsTUFBTUEsZUFBNkI7SUFDeEM7UUFBRUMsS0FBSztRQUFHQyxLQUFLO1FBQUlDLE9BQU87UUFBV0MsT0FBTztJQUFPO0lBQ25EO1FBQUVILEtBQUs7UUFBSUMsS0FBSztRQUFJQyxPQUFPO1FBQVdDLE9BQU87SUFBUTtJQUNyRDtRQUFFSCxLQUFLO1FBQUlDLEtBQUs7UUFBS0MsT0FBTztRQUFXQyxPQUFPO0lBQVM7SUFDdkQ7UUFBRUgsS0FBSztRQUFLQyxLQUFLO1FBQUtDLE9BQU87UUFBV0MsT0FBTztJQUFVO0lBQ3pEO1FBQUVILEtBQUs7UUFBS0MsS0FBSztRQUFLQyxPQUFPO1FBQVdDLE9BQU87SUFBVTtJQUN6RDtRQUFFSCxLQUFLO1FBQUtDLEtBQUs7UUFBS0MsT0FBTztRQUFXQyxPQUFPO0lBQVU7SUFDekQ7UUFBRUgsS0FBSztRQUFLQyxLQUFLO1FBQUtDLE9BQU87UUFBV0MsT0FBTztJQUFVO0lBQ3pEO1FBQUVILEtBQUs7UUFBS0MsS0FBS0c7UUFBVUYsT0FBTztRQUFXQyxPQUFPO0lBQU87Q0FDNUQsQ0FBQztBQUVGLGFBQWE7QUFDTixTQUFTRSxnQkFBZ0JDLEtBQWE7SUFDM0MsTUFBTUMsUUFBUVIsYUFBYVMsSUFBSSxDQUFDQyxDQUFBQSxJQUFLSCxTQUFTRyxFQUFFVCxHQUFHLElBQUlNLFNBQVNHLEVBQUVSLEdBQUc7SUFDckUsT0FBT00sQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPTCxLQUFLLEtBQUlILFlBQVksQ0FBQyxFQUFFLENBQUNHLEtBQUs7QUFDOUM7QUFFQSxPQUFPO0FBQ0EsU0FBU1EsYUFBYUMsTUFBaUIsRUFBRUMsUUFBZ0I7SUFDOUQsTUFBTUMsUUFBb0IsRUFBRTtJQUU1QixTQUFTO0lBQ1QsTUFBTUMsV0FBV0MsS0FBS0MsSUFBSSxDQUFDLENBQUNMLE9BQU9NLEtBQUssR0FBR04sT0FBT08sS0FBSyxJQUFJTjtJQUMzRCxNQUFNTyxXQUFXSixLQUFLQyxJQUFJLENBQUMsQ0FBQ0wsT0FBT1MsSUFBSSxHQUFHVCxPQUFPVSxJQUFJLElBQUlUO0lBRXpELElBQUssSUFBSVUsSUFBSSxHQUFHQSxJQUFJUixVQUFVUSxJQUFLO1FBQ2pDLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJSixVQUFVSSxJQUFLO1lBQ2pDLE1BQU1MLFFBQVFQLE9BQU9PLEtBQUssR0FBR0ksSUFBSVY7WUFDakMsTUFBTUssUUFBUUYsS0FBS2YsR0FBRyxDQUFDVyxPQUFPTSxLQUFLLEVBQUVDLFFBQVFOO1lBQzdDLE1BQU1TLE9BQU9WLE9BQU9VLElBQUksR0FBR0UsSUFBSVg7WUFDL0IsTUFBTVEsT0FBT0wsS0FBS2YsR0FBRyxDQUFDVyxPQUFPUyxJQUFJLEVBQUVDLE9BQU9UO1lBRTFDLE1BQU1ZLE9BQWlCO2dCQUNyQkMsSUFBSSxRQUFhRixPQUFMRCxHQUFFLEtBQUssT0FBRkM7Z0JBQ2pCWixRQUFRO29CQUFFTTtvQkFBT0M7b0JBQU9FO29CQUFNQztnQkFBSztnQkFDbkNLLFFBQVE7b0JBQ05DLEtBQUssQ0FBQ1YsUUFBUUMsS0FBSSxJQUFLO29CQUN2QlUsS0FBSyxDQUFDUixPQUFPQyxJQUFHLElBQUs7Z0JBQ3ZCO2dCQUNBUSxjQUFjO2dCQUNkM0IsT0FBT0gsWUFBWSxDQUFDLEVBQUUsQ0FBQ0csS0FBSztZQUM5QjtZQUVBVyxNQUFNaUIsSUFBSSxDQUFDTjtRQUNiO0lBQ0Y7SUFFQSxPQUFPWDtBQUNUO0FBRUEsY0FBYztBQUNQLFNBQVNrQixxQkFDZGxCLEtBQWlCLEVBQ2pCbUIsUUFBdUI7SUFFdkIsY0FBYztJQUNkbkIsTUFBTW9CLE9BQU8sQ0FBQ1QsQ0FBQUE7UUFDWkEsS0FBS0ssWUFBWSxHQUFHO0lBQ3RCO0lBRUEsY0FBYztJQUNkRyxTQUFTQyxPQUFPLENBQUNDLENBQUFBO1FBQ2YsTUFBTVYsT0FBT1gsTUFBTUwsSUFBSSxDQUFDMkIsQ0FBQUEsSUFDdEJELFFBQVFQLEdBQUcsSUFBSVEsRUFBRXhCLE1BQU0sQ0FBQ08sS0FBSyxJQUM3QmdCLFFBQVFQLEdBQUcsR0FBR1EsRUFBRXhCLE1BQU0sQ0FBQ00sS0FBSyxJQUM1QmlCLFFBQVFOLEdBQUcsSUFBSU8sRUFBRXhCLE1BQU0sQ0FBQ1UsSUFBSSxJQUM1QmEsUUFBUU4sR0FBRyxHQUFHTyxFQUFFeEIsTUFBTSxDQUFDUyxJQUFJO1FBRzdCLElBQUlJLE1BQU07WUFDUkEsS0FBS0ssWUFBWSxJQUFJSyxRQUFRNUIsS0FBSztRQUNwQztJQUNGO0lBRUEsU0FBUztJQUNUTyxNQUFNb0IsT0FBTyxDQUFDVCxDQUFBQTtRQUNaQSxLQUFLdEIsS0FBSyxHQUFHRyxnQkFBZ0JtQixLQUFLSyxZQUFZO0lBQ2hEO0lBRUEsT0FBT2hCO0FBQ1Q7QUFFQSxXQUFXO0FBQ0osU0FBU3VCLHFCQUFxQnpCLE1BQWlCLEVBQUVMLEtBQWE7SUFDbkUsTUFBTTBCLFdBQTBCLEVBQUU7SUFFbEMsSUFBSyxJQUFJVixJQUFJLEdBQUdBLElBQUloQixPQUFPZ0IsSUFBSztRQUM5QixNQUFNSyxNQUFNaEIsT0FBT08sS0FBSyxHQUFHSCxLQUFLc0IsTUFBTSxLQUFNMUIsQ0FBQUEsT0FBT00sS0FBSyxHQUFHTixPQUFPTyxLQUFLO1FBQ3ZFLE1BQU1VLE1BQU1qQixPQUFPVSxJQUFJLEdBQUdOLEtBQUtzQixNQUFNLEtBQU0xQixDQUFBQSxPQUFPUyxJQUFJLEdBQUdULE9BQU9VLElBQUk7UUFDcEUsTUFBTVEsZUFBZWQsS0FBS3VCLEtBQUssQ0FBQ3ZCLEtBQUtzQixNQUFNLEtBQUssT0FBTztRQUV2REwsU0FBU0YsSUFBSSxDQUFDO1lBQ1pMLElBQUksV0FBYSxPQUFGSDtZQUNmSztZQUNBQztZQUNBdEIsT0FBT3VCO1FBQ1Q7SUFDRjtJQUVBLE9BQU9HO0FBQ1Q7QUFFQSxVQUFVO0FBQ0gsU0FBU08sWUFBWWpDLEtBQWE7SUFDdkMsSUFBSUEsU0FBUyxNQUFNO1FBQ2pCLE9BQU8sR0FBNkIsT0FBMUIsQ0FBQ0EsUUFBUSxJQUFHLEVBQUdrQyxPQUFPLENBQUMsSUFBRztJQUN0QztJQUNBLE9BQU9sQyxNQUFNbUMsUUFBUTtBQUN2QjtBQUVBLGtCQUFrQjtBQUNYLFNBQVNDLGtCQUFrQkMsSUFBWTtJQUM1QyxvQkFBb0I7SUFDcEIsSUFBSUEsUUFBUSxJQUFJLE9BQU8sTUFBUSxxQkFBcUI7SUFDcEQsSUFBSUEsUUFBUSxJQUFJLE9BQU8sTUFBUSxzQkFBc0I7SUFDckQsSUFBSUEsUUFBUSxHQUFHLE9BQU8sTUFBUyxxQkFBcUI7SUFDcEQsSUFBSUEsUUFBUSxHQUFHLE9BQU8sS0FBUyxzQkFBc0I7SUFDckQsSUFBSUEsUUFBUSxHQUFHLE9BQU8sS0FBUyxxQkFBcUI7SUFDcEQsT0FBTyxLQUF3QixzQkFBc0I7QUFDdkQ7QUFFQSxTQUFTO0FBQ0YsU0FBU0MsZ0JBQWdCbEIsTUFBd0IsRUFBRWlCLElBQVk7SUFDcEUsZ0JBQWdCO0lBQ2hCLE1BQU1FLFdBQVcsTUFBTTlCLEtBQUsrQixHQUFHLENBQUMsR0FBR0gsT0FBTztJQUMxQyxNQUFNSSxXQUFXLE1BQU1oQyxLQUFLK0IsR0FBRyxDQUFDLEdBQUdILE9BQU87SUFFMUMsT0FBTztRQUNMMUIsT0FBT1MsTUFBTSxDQUFDLEVBQUUsR0FBR21CLFdBQVc7UUFDOUIzQixPQUFPUSxNQUFNLENBQUMsRUFBRSxHQUFHbUIsV0FBVztRQUM5QnpCLE1BQU1NLE1BQU0sQ0FBQyxFQUFFLEdBQUdxQixXQUFXO1FBQzdCMUIsTUFBTUssTUFBTSxDQUFDLEVBQUUsR0FBR3FCLFdBQVc7SUFDL0I7QUFDRiIsInNvdXJjZXMiOlsiQzpcXHdvcmtzcGFjZXNcXGRkXFxtYmRwXFxmcm9udGVuZFxcbGliXFxob3RzcG90LXV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENvbG9yR3JhZGUsIEdyaWRDZWxsLCBNYXBCb3VuZHMsIEhvdHNwb3REYXRhIH0gZnJvbSAnLi9ob3RzcG90LXR5cGVzJztcblxuLy8gZUJpcmQg6aOO5qC855qE6aKc6Imy5YiG57qn6YWN572uXG5leHBvcnQgY29uc3QgQ09MT1JfR1JBREVTOiBDb2xvckdyYWRlW10gPSBbXG4gIHsgbWluOiAwLCBtYXg6IDEwLCBjb2xvcjogJyNGRkVEQTAnLCBsYWJlbDogJzAtMTAnIH0sXG4gIHsgbWluOiAxMSwgbWF4OiA1MCwgY29sb3I6ICcjRkVEOTc2JywgbGFiZWw6ICcxMS01MCcgfSxcbiAgeyBtaW46IDUxLCBtYXg6IDEwMCwgY29sb3I6ICcjRkVCMjRDJywgbGFiZWw6ICc1MS0xMDAnIH0sXG4gIHsgbWluOiAxMDEsIG1heDogMTUwLCBjb2xvcjogJyNGRDhEM0MnLCBsYWJlbDogJzEwMS0xNTAnIH0sXG4gIHsgbWluOiAxNTEsIG1heDogMjAwLCBjb2xvcjogJyNGQzRFMkEnLCBsYWJlbDogJzE1MS0yMDAnIH0sXG4gIHsgbWluOiAyMDEsIG1heDogMzAwLCBjb2xvcjogJyNFMzFBMUMnLCBsYWJlbDogJzIwMS0zMDAnIH0sXG4gIHsgbWluOiAzMDEsIG1heDogNTAwLCBjb2xvcjogJyNCRDAwMjYnLCBsYWJlbDogJzMwMS01MDAnIH0sXG4gIHsgbWluOiA1MDEsIG1heDogSW5maW5pdHksIGNvbG9yOiAnIzgwMDAyNicsIGxhYmVsOiAnNTAwKycgfSxcbl07XG5cbi8vIOagueaNrueDreeCueaVsOmHj+iOt+WPluminOiJslxuZXhwb3J0IGZ1bmN0aW9uIGdldENvbG9yQnlDb3VudChjb3VudDogbnVtYmVyKTogc3RyaW5nIHtcbiAgY29uc3QgZ3JhZGUgPSBDT0xPUl9HUkFERVMuZmluZChnID0+IGNvdW50ID49IGcubWluICYmIGNvdW50IDw9IGcubWF4KTtcbiAgcmV0dXJuIGdyYWRlPy5jb2xvciB8fCBDT0xPUl9HUkFERVNbMF0uY29sb3I7XG59XG5cbi8vIOeUn+aIkOe9keagvFxuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlR3JpZChib3VuZHM6IE1hcEJvdW5kcywgZ3JpZFNpemU6IG51bWJlcik6IEdyaWRDZWxsW10ge1xuICBjb25zdCBjZWxsczogR3JpZENlbGxbXSA9IFtdO1xuICBcbiAgLy8g6K6h566X572R5qC85pWw6YePXG4gIGNvbnN0IGxhdFN0ZXBzID0gTWF0aC5jZWlsKChib3VuZHMubm9ydGggLSBib3VuZHMuc291dGgpIC8gZ3JpZFNpemUpO1xuICBjb25zdCBsbmdTdGVwcyA9IE1hdGguY2VpbCgoYm91bmRzLmVhc3QgLSBib3VuZHMud2VzdCkgLyBncmlkU2l6ZSk7XG4gIFxuICBmb3IgKGxldCBpID0gMDsgaSA8IGxhdFN0ZXBzOyBpKyspIHtcbiAgICBmb3IgKGxldCBqID0gMDsgaiA8IGxuZ1N0ZXBzOyBqKyspIHtcbiAgICAgIGNvbnN0IHNvdXRoID0gYm91bmRzLnNvdXRoICsgaSAqIGdyaWRTaXplO1xuICAgICAgY29uc3Qgbm9ydGggPSBNYXRoLm1pbihib3VuZHMubm9ydGgsIHNvdXRoICsgZ3JpZFNpemUpO1xuICAgICAgY29uc3Qgd2VzdCA9IGJvdW5kcy53ZXN0ICsgaiAqIGdyaWRTaXplO1xuICAgICAgY29uc3QgZWFzdCA9IE1hdGgubWluKGJvdW5kcy5lYXN0LCB3ZXN0ICsgZ3JpZFNpemUpO1xuICAgICAgXG4gICAgICBjb25zdCBjZWxsOiBHcmlkQ2VsbCA9IHtcbiAgICAgICAgaWQ6IGBjZWxsLSR7aX0tJHtqfWAsXG4gICAgICAgIGJvdW5kczogeyBub3J0aCwgc291dGgsIGVhc3QsIHdlc3QgfSxcbiAgICAgICAgY2VudGVyOiB7XG4gICAgICAgICAgbGF0OiAobm9ydGggKyBzb3V0aCkgLyAyLFxuICAgICAgICAgIGxuZzogKGVhc3QgKyB3ZXN0KSAvIDIsXG4gICAgICAgIH0sXG4gICAgICAgIGhvdHNwb3RDb3VudDogMCxcbiAgICAgICAgY29sb3I6IENPTE9SX0dSQURFU1swXS5jb2xvcixcbiAgICAgIH07XG4gICAgICBcbiAgICAgIGNlbGxzLnB1c2goY2VsbCk7XG4gICAgfVxuICB9XG4gIFxuICByZXR1cm4gY2VsbHM7XG59XG5cbi8vIOWwhueDreeCueaVsOaNruWIhumFjeWIsOe9keagvOS4rVxuZXhwb3J0IGZ1bmN0aW9uIGFzc2lnbkhvdHNwb3RzVG9HcmlkKFxuICBjZWxsczogR3JpZENlbGxbXSxcbiAgaG90c3BvdHM6IEhvdHNwb3REYXRhW11cbik6IEdyaWRDZWxsW10ge1xuICAvLyDph43nva7miYDmnInnvZHmoLznmoTng63ngrnmlbDph49cbiAgY2VsbHMuZm9yRWFjaChjZWxsID0+IHtcbiAgICBjZWxsLmhvdHNwb3RDb3VudCA9IDA7XG4gIH0pO1xuICBcbiAgLy8g5bCG54Ot54K55YiG6YWN5Yiw5a+55bqU55qE572R5qC8XG4gIGhvdHNwb3RzLmZvckVhY2goaG90c3BvdCA9PiB7XG4gICAgY29uc3QgY2VsbCA9IGNlbGxzLmZpbmQoYyA9PiBcbiAgICAgIGhvdHNwb3QubGF0ID49IGMuYm91bmRzLnNvdXRoICYmXG4gICAgICBob3RzcG90LmxhdCA8IGMuYm91bmRzLm5vcnRoICYmXG4gICAgICBob3RzcG90LmxuZyA+PSBjLmJvdW5kcy53ZXN0ICYmXG4gICAgICBob3RzcG90LmxuZyA8IGMuYm91bmRzLmVhc3RcbiAgICApO1xuICAgIFxuICAgIGlmIChjZWxsKSB7XG4gICAgICBjZWxsLmhvdHNwb3RDb3VudCArPSBob3RzcG90LmNvdW50O1xuICAgIH1cbiAgfSk7XG4gIFxuICAvLyDmm7TmlrDnvZHmoLzpopzoibJcbiAgY2VsbHMuZm9yRWFjaChjZWxsID0+IHtcbiAgICBjZWxsLmNvbG9yID0gZ2V0Q29sb3JCeUNvdW50KGNlbGwuaG90c3BvdENvdW50KTtcbiAgfSk7XG4gIFxuICByZXR1cm4gY2VsbHM7XG59XG5cbi8vIOeUn+aIkOaooeaLn+eDreeCueaVsOaNrlxuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlTW9ja0hvdHNwb3RzKGJvdW5kczogTWFwQm91bmRzLCBjb3VudDogbnVtYmVyKTogSG90c3BvdERhdGFbXSB7XG4gIGNvbnN0IGhvdHNwb3RzOiBIb3RzcG90RGF0YVtdID0gW107XG4gIFxuICBmb3IgKGxldCBpID0gMDsgaSA8IGNvdW50OyBpKyspIHtcbiAgICBjb25zdCBsYXQgPSBib3VuZHMuc291dGggKyBNYXRoLnJhbmRvbSgpICogKGJvdW5kcy5ub3J0aCAtIGJvdW5kcy5zb3V0aCk7XG4gICAgY29uc3QgbG5nID0gYm91bmRzLndlc3QgKyBNYXRoLnJhbmRvbSgpICogKGJvdW5kcy5lYXN0IC0gYm91bmRzLndlc3QpO1xuICAgIGNvbnN0IGhvdHNwb3RDb3VudCA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDEwMCkgKyAxO1xuICAgIFxuICAgIGhvdHNwb3RzLnB1c2goe1xuICAgICAgaWQ6IGBob3RzcG90LSR7aX1gLFxuICAgICAgbGF0LFxuICAgICAgbG5nLFxuICAgICAgY291bnQ6IGhvdHNwb3RDb3VudCxcbiAgICB9KTtcbiAgfVxuICBcbiAgcmV0dXJuIGhvdHNwb3RzO1xufVxuXG4vLyDmoLzlvI/ljJbmlbDlrZfmmL7npLpcbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRDb3VudChjb3VudDogbnVtYmVyKTogc3RyaW5nIHtcbiAgaWYgKGNvdW50ID49IDEwMDApIHtcbiAgICByZXR1cm4gYCR7KGNvdW50IC8gMTAwMCkudG9GaXhlZCgxKX1rYDtcbiAgfVxuICByZXR1cm4gY291bnQudG9TdHJpbmcoKTtcbn1cblxuLy8g5qC55o2u57yp5pS+57qn5Yir6K6h566X5ZCI6YCC55qE572R5qC85aSn5bCPXG5leHBvcnQgZnVuY3Rpb24gZ2V0R3JpZFNpemVCeVpvb20oem9vbTogbnVtYmVyKTogbnVtYmVyIHtcbiAgLy8g57yp5pS+57qn5Yir6LaK6auY77yM572R5qC86LaK5bCP77yM5pi+56S66LaK57K+57uGXG4gIGlmICh6b29tID49IDEyKSByZXR1cm4gMC4wMTsgICAvLyDpq5jnvKnmlL7vvJowLjAx5bqm572R5qC8ICh+MWttKVxuICBpZiAoem9vbSA+PSAxMCkgcmV0dXJuIDAuMDI7ICAgLy8g5Lit6auY57yp5pS+77yaMC4wMuW6pue9keagvCAofjJrbSlcbiAgaWYgKHpvb20gPj0gOCkgcmV0dXJuIDAuMDU7ICAgIC8vIOS4ree8qeaUvu+8mjAuMDXluqbnvZHmoLwgKH41a20pXG4gIGlmICh6b29tID49IDYpIHJldHVybiAwLjE7ICAgICAvLyDkuK3kvY7nvKnmlL7vvJowLjHluqbnvZHmoLwgKH4xMGttKVxuICBpZiAoem9vbSA+PSA0KSByZXR1cm4gMC4yOyAgICAgLy8g5L2O57yp5pS+77yaMC4y5bqm572R5qC8ICh+MjBrbSlcbiAgcmV0dXJuIDAuNTsgICAgICAgICAgICAgICAgICAgIC8vIOW+iOS9jue8qeaUvu+8mjAuNeW6pue9keagvCAofjUwa20pXG59XG5cbi8vIOiuoeeul+WcsOWbvui+ueeVjFxuZXhwb3J0IGZ1bmN0aW9uIGNhbGN1bGF0ZUJvdW5kcyhjZW50ZXI6IFtudW1iZXIsIG51bWJlcl0sIHpvb206IG51bWJlcik6IE1hcEJvdW5kcyB7XG4gIC8vIOagueaNrue8qeaUvue6p+WIq+iuoeeul+WQiOmAgueahOi+ueeVjFxuICBjb25zdCBsYXRSYW5nZSA9IDE4MCAvIE1hdGgucG93KDIsIHpvb20gLSAxKTtcbiAgY29uc3QgbG5nUmFuZ2UgPSAzNjAgLyBNYXRoLnBvdygyLCB6b29tIC0gMSk7XG5cbiAgcmV0dXJuIHtcbiAgICBub3J0aDogY2VudGVyWzBdICsgbGF0UmFuZ2UgLyAyLFxuICAgIHNvdXRoOiBjZW50ZXJbMF0gLSBsYXRSYW5nZSAvIDIsXG4gICAgZWFzdDogY2VudGVyWzFdICsgbG5nUmFuZ2UgLyAyLFxuICAgIHdlc3Q6IGNlbnRlclsxXSAtIGxuZ1JhbmdlIC8gMixcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJDT0xPUl9HUkFERVMiLCJtaW4iLCJtYXgiLCJjb2xvciIsImxhYmVsIiwiSW5maW5pdHkiLCJnZXRDb2xvckJ5Q291bnQiLCJjb3VudCIsImdyYWRlIiwiZmluZCIsImciLCJnZW5lcmF0ZUdyaWQiLCJib3VuZHMiLCJncmlkU2l6ZSIsImNlbGxzIiwibGF0U3RlcHMiLCJNYXRoIiwiY2VpbCIsIm5vcnRoIiwic291dGgiLCJsbmdTdGVwcyIsImVhc3QiLCJ3ZXN0IiwiaSIsImoiLCJjZWxsIiwiaWQiLCJjZW50ZXIiLCJsYXQiLCJsbmciLCJob3RzcG90Q291bnQiLCJwdXNoIiwiYXNzaWduSG90c3BvdHNUb0dyaWQiLCJob3RzcG90cyIsImZvckVhY2giLCJob3RzcG90IiwiYyIsImdlbmVyYXRlTW9ja0hvdHNwb3RzIiwicmFuZG9tIiwiZmxvb3IiLCJmb3JtYXRDb3VudCIsInRvRml4ZWQiLCJ0b1N0cmluZyIsImdldEdyaWRTaXplQnlab29tIiwiem9vbSIsImNhbGN1bGF0ZUJvdW5kcyIsImxhdFJhbmdlIiwicG93IiwibG5nUmFuZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/hotspot-utils.ts\n"));

/***/ })

});