'use client';

import React from 'react';
import { COLOR_GRADES } from '@/lib/hotspot-utils';

interface HotspotLegendProps {
  className?: string;
}

export function HotspotLegend({ className = '' }: HotspotLegendProps) {
  return (
    <div className={`bg-white rounded-lg shadow-lg p-4 ${className}`}>
      <h3 className="text-sm font-semibold text-gray-700 mb-3">
        观察热点数量
      </h3>
      <div className="space-y-1">
        {COLOR_GRADES.map((grade, index) => (
          <div key={index} className="flex items-center space-x-2">
            <div
              className="w-4 h-4 rounded-sm border border-gray-300"
              style={{ backgroundColor: grade.color }}
            />
            <span className="text-xs text-gray-600 font-medium">
              {grade.label}
            </span>
          </div>
        ))}
      </div>
      <div className="mt-4 pt-3 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          每个网格显示该区域内的热点数量
        </p>
      </div>
    </div>
  );
}

export default HotspotLegend;
