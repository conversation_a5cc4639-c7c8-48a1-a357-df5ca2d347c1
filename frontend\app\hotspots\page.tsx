'use client';

import React, { useState, useCallback } from 'react';
import dynamic from 'next/dynamic';
import { MapLayerType, GridCell } from '@/lib/hotspot-types';
import SearchBar from '@/components/search-bar';
import HotspotLegend from '@/components/hotspot-legend';
import MapControls from '@/components/map-controls';
import InfoPanel from '@/components/info-panel';
import { Bird, Menu, X } from 'lucide-react';

// 动态导入地图组件以避免 SSR 问题
const HotspotMap = dynamic(() => import('@/components/hotspot-map'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
      <div className="flex items-center space-x-2 text-gray-600">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <span>加载地图组件中...</span>
      </div>
    </div>
  ),
});

export default function HotspotsPage() {
  // 地图状态
  const [mapCenter, setMapCenter] = useState<[number, number]>([39.9042, 116.4074]); // 北京
  const [mapZoom, setMapZoom] = useState(8);
  const [layerType, setLayerType] = useState<MapLayerType>('street');
  
  // UI 状态
  const [hoveredCell, setHoveredCell] = useState<GridCell | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  // 统计数据（模拟）
  const [totalHotspots] = useState(12847);
  const [visibleHotspots] = useState(3421);

  // 处理地图移动
  const handleMapMove = useCallback((center: [number, number], zoom: number) => {
    setMapCenter(center);
    setMapZoom(zoom);
  }, []);

  // 处理位置搜索
  const handleLocationSelect = useCallback((location: { lat: number; lng: number; name: string }) => {
    setMapCenter([location.lat, location.lng]);
    setMapZoom(12);
  }, []);

  // 处理网格悬停
  const handleGridCellHover = useCallback((cell: GridCell | null) => {
    setHoveredCell(cell);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo 和标题 */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Bird className="h-8 w-8 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900">观鸟热点</h1>
              </div>
              <div className="hidden sm:block text-sm text-gray-500">
                发现最佳观鸟地点
              </div>
            </div>

            {/* 搜索栏 */}
            <div className="flex-1 max-w-lg mx-8">
              <SearchBar 
                onLocationSelect={handleLocationSelect}
                placeholder="搜索地点或坐标..."
              />
            </div>

            {/* 移动端菜单按钮 */}
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              {sidebarOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <div className="flex-1 flex">
        {/* 地图区域 */}
        <main className="flex-1 p-4">
          <div className="h-[calc(100vh-120px)] relative">
            <HotspotMap
              center={mapCenter}
              zoom={mapZoom}
              layerType={layerType}
              onMapMove={handleMapMove}
              onGridCellHover={handleGridCellHover}
              className="w-full h-full"
            />
          </div>
        </main>

        {/* 右侧边栏 */}
        <aside className={`
          w-80 bg-white border-l border-gray-200 p-4 space-y-4 overflow-y-auto
          ${sidebarOpen ? 'block' : 'hidden lg:block'}
          lg:relative absolute right-0 top-16 bottom-0 z-10 shadow-lg lg:shadow-none
        `}>
          {/* 信息面板 */}
          <InfoPanel
            hoveredCell={hoveredCell}
            totalHotspots={totalHotspots}
            visibleHotspots={visibleHotspots}
            currentZoom={mapZoom}
          />

          {/* 图例 */}
          <HotspotLegend />

          {/* 地图控件 */}
          <MapControls
            currentLayer={layerType}
            onLayerChange={setLayerType}
          />

          {/* 额外信息 */}
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <h4 className="font-semibold text-blue-900 mb-2">使用说明</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 拖拽地图查看不同区域</li>
              <li>• 悬停网格查看热点数量</li>
              <li>• 使用搜索框快速定位</li>
              <li>• 切换地图类型获得更好视觉效果</li>
            </ul>
          </div>
        </aside>
      </div>

      {/* 移动端遮罩 */}
      {sidebarOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-5"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}
