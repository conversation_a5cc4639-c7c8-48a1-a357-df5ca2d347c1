"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { X, Map, CheckCircle, Info } from "lucide-react"
import { getMapServiceConfig } from "@/lib/map-services"

interface MapServiceNotificationProps {
  message: string | null
  newService: string | null
  onDismiss: () => void
  onUndo?: () => void
  autoHideDelay?: number
  className?: string
}

export default function MapServiceNotification({
  message,
  newService,
  onDismiss,
  onUndo,
  autoHideDelay = 5000,
  className = ""
}: MapServiceNotificationProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (message && newService) {
      setIsVisible(true)
      setIsAnimating(true)
      
      // 自动隐藏
      const hideTimer = setTimeout(() => {
        handleDismiss()
      }, autoHideDelay)

      return () => clearTimeout(hideTimer)
    } else {
      setIsVisible(false)
    }
  }, [message, newService, autoHideDelay])

  const handleDismiss = () => {
    setIsAnimating(false)
    setTimeout(() => {
      setIsVisible(false)
      onDismiss()
    }, 200) // 等待动画完成
  }

  const handleUndo = () => {
    if (onUndo) {
      onUndo()
    }
    handleDismiss()
  }

  if (!isVisible || !message || !newService) {
    return null
  }

  const serviceConfig = getMapServiceConfig(newService)
  const serviceName = serviceConfig?.name || newService

  const getServiceBadgeColor = (serviceId: string): string => {
    return "bg-blue-500" // 统一使用蓝色，因为只有高德地图
  }

  return (
    <div 
      className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-[1001] transition-all duration-200 ${
        isAnimating ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
      } ${className}`}
    >
      <Card className="bg-white shadow-lg border-l-4 border-l-blue-500 max-w-md">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            {/* 图标 */}
            <div className="flex-shrink-0 mt-0.5">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Map className="w-4 h-4 text-blue-600" />
              </div>
            </div>

            {/* 内容 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium text-gray-900">
                  地图服务已切换
                </span>
              </div>
              
              <div className="text-sm text-gray-600 mb-2">
                {message}
              </div>

              <div className="flex items-center gap-2">
                <Badge 
                  className={`${getServiceBadgeColor(newService)} text-white text-xs`}
                >
                  {serviceName}
                </Badge>
                {serviceConfig?.detailLevel === 'high' && (
                  <Badge variant="outline" className="text-xs">
                    高详细度
                  </Badge>
                )}
                {serviceConfig?.globalCoverage && (
                  <Badge variant="outline" className="text-xs">
                    全球覆盖
                  </Badge>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center gap-1 flex-shrink-0">
              {onUndo && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleUndo}
                  className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                >
                  撤销
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>

          {/* 额外信息 */}
          {serviceConfig?.description && (
            <div className="mt-2 pt-2 border-t border-gray-100">
              <div className="flex items-start gap-2">
                <Info className="w-3 h-3 text-gray-400 mt-0.5 flex-shrink-0" />
                <span className="text-xs text-gray-500">
                  {serviceConfig.description}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
