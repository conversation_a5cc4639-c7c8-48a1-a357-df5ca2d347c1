'use client';

import React from 'react';
import { MapLayerType } from '@/lib/hotspot-types';
import { Map, Satellite, Mountain, Layers } from 'lucide-react';

interface MapControlsProps {
  currentLayer: MapLayerType;
  onLayerChange: (layer: MapLayerType) => void;
  className?: string;
}

const layerOptions = [
  {
    type: 'street' as MapLayerType,
    label: '街道图',
    icon: Map,
    description: '标准街道地图',
  },
  {
    type: 'satellite' as MapLayerType,
    label: '卫星图',
    icon: Satellite,
    description: '卫星影像',
  },
  {
    type: 'terrain' as MapLayerType,
    label: '地形图',
    icon: Mountain,
    description: '地形地貌',
  },
  {
    type: 'hybrid' as MapLayerType,
    label: '混合图',
    icon: Layers,
    description: '卫星图+标注',
  },
];

export function MapControls({ currentLayer, onLayerChange, className = '' }: MapControlsProps) {
  return (
    <div className={`bg-white rounded-lg shadow-lg p-4 ${className}`}>
      <h3 className="text-sm font-semibold text-gray-700 mb-3">
        地图类型
      </h3>
      <div className="space-y-2">
        {layerOptions.map((option) => {
          const IconComponent = option.icon;
          const isSelected = currentLayer === option.type;
          
          return (
            <button
              key={option.type}
              onClick={() => onLayerChange(option.type)}
              className={`w-full flex items-center space-x-3 p-2 rounded-md text-left transition-colors
                        ${isSelected 
                          ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                          : 'hover:bg-gray-50 text-gray-700 border border-transparent'
                        }`}
            >
              <div className={`p-1 rounded ${isSelected ? 'bg-blue-100' : 'bg-gray-100'}`}>
                <IconComponent className="h-4 w-4" />
              </div>
              <div className="flex-1">
                <div className="font-medium text-sm">{option.label}</div>
                <div className="text-xs text-gray-500">{option.description}</div>
              </div>
              {isSelected && (
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
              )}
            </button>
          );
        })}
      </div>
      
      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>网格工具</span>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full" />
            <span>已启用</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MapControls;
