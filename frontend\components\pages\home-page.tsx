"use client"

import { useState, useEffect } from "react"
import dynamic from "next/dynamic"
import MainLayout from "@/components/layout/main-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, TrendingUp, Volume2, Filter, Layers, Menu } from "lucide-react"
import { getMapServiceOptions, getDefaultMapService } from "@/lib/map-services"

// 动态导入全屏地图组件，避免SSR问题
const FullScreenMap = dynamic(() => import("@/components/map/full-screen-map"), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-gray-100 animate-pulse flex items-center justify-center">
      <div className="text-gray-500">加载地图中...</div>
    </div>
  ),
})

export default function HomePage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [stats, setStats] = useState({
    species: 6096,
    recordings: 24837,
    hotspots: 32,
    onlineUsers: 156,
  })

  // 地图切换功能状态
  const [isControlPanelOpen, setIsControlPanelOpen] = useState(true)
  const [selectedMapService, setSelectedMapService] = useState(getDefaultMapService().id) // 默认使用高德地图
  const [heatmapOpacity, setHeatmapOpacity] = useState([70])
  const [showHeatmap, setShowHeatmap] = useState(true)
  const [showDistribution, setShowDistribution] = useState(true)
  const [showCollectionPoints, setShowCollectionPoints] = useState(true)
  const [selectedSpecies, setSelectedSpecies] = useState("all")
  const [selectedTimeRange, setSelectedTimeRange] = useState("all")
  const [hideVillageMarkers, setHideVillageMarkers] = useState(true)

  // 获取地图服务选项
  const mapServiceOptions = getMapServiceOptions()

  const speciesCategories = [
    { value: "all", label: "全部物种" },
    { value: "cetaceans", label: "鲸豚类" },
    { value: "fish", label: "鱼类" },
    { value: "crustaceans", label: "甲壳动物" },
    { value: "mollusks", label: "软体动物" },
  ]

  // 模拟实时数据更新
  useEffect(() => {
    const interval = setInterval(() => {
      setStats((prev) => {
        // 使用时间戳作为种子生成固定变化，避免Math.random()
        const timestamp = Date.now()
        const change = ((timestamp / 1000) % 10) - 5
        return {
          ...prev,
          onlineUsers: Math.max(100, prev.onlineUsers + Math.floor(change)),
        }
      })
    }, 30000)

    return () => clearInterval(interval)
  }, [])

  const handleSearch = () => {
    if (searchQuery.trim()) {
      window.location.href = `/species?q=${encodeURIComponent(searchQuery)}`
    }
  }

  return (
    <MainLayout>
      <div className="h-screen flex flex-col">
       

        {/* 主要内容区域 */}
        <div className="flex-1 flex">
          {/* 地图容器 - 占据主要空间 */}
          <div className="flex-1 relative">
            <FullScreenMap
              mapService={selectedMapService}
              showHeatmap={showHeatmap}
              showDistribution={showDistribution}
              showCollectionPoints={showCollectionPoints}
              heatmapOpacity={heatmapOpacity[0]}
              selectedSpecies={selectedSpecies}
              selectedTimeRange={selectedTimeRange}
              hideVillageMarkers={hideVillageMarkers}
              showMarkerLayers={true}
            />

            {/* 地图控制按钮 */}
            <div className="absolute top-4 left-4 z-10 flex space-x-2">
              <Button
                variant="outline"
                size="icon"
                className="bg-white/90 backdrop-blur-sm"
                onClick={() => setIsControlPanelOpen(!isControlPanelOpen)}
              >
                <Menu className="w-4 h-4" />
              </Button>

              <Select value={selectedMapService} onValueChange={setSelectedMapService}>
                <SelectTrigger className="w-48 bg-white/90 backdrop-blur-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {mapServiceOptions.map((service) => (
                    <SelectItem key={service.value} value={service.value}>
                      <div>
                        <div className="font-medium">{service.label}</div>
                        <div className="text-xs text-gray-500">{service.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 右侧边栏 */}
          <div className={`w-80 bg-white border-l border-gray-200 overflow-y-auto shadow-lg transition-transform duration-300 ${
            isControlPanelOpen ? "translate-x-0" : "translate-x-full"
          }`}>
            {/* 控制面板标题 */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold">地图控制面板</h2>
                <Button variant="ghost" size="icon" onClick={() => setIsControlPanelOpen(false)}>
                  ×
                </Button>
              </div>
            </div>

            {/* 图层控制 */}
            <div className="p-6 border-b border-gray-200">
              <h3 className="font-semibold mb-3 flex items-center">
                <Layers className="w-4 h-4 mr-2" />
                图层控制
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <label className="text-sm">海域热力图</label>
                  <Switch checked={showHeatmap} onCheckedChange={setShowHeatmap} />
                </div>

                {showHeatmap && (
                  <div className="ml-4">
                    <label className="text-xs text-gray-600 mb-2 block">透明度: {heatmapOpacity[0]}%</label>
                    <Slider
                      value={heatmapOpacity}
                      onValueChange={setHeatmapOpacity}
                      max={100}
                      step={10}
                      className="w-full"
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <label className="text-sm">物种分布区</label>
                  <Switch checked={showDistribution} onCheckedChange={setShowDistribution} />
                </div>

                <div className="flex items-center justify-between">
                  <label className="text-sm">监测站点</label>
                  <Switch checked={showCollectionPoints} onCheckedChange={setShowCollectionPoints} />
                </div>

                <div className="flex items-center justify-between">
                  <label className="text-sm">隐藏村庄标记</label>
                  <Switch checked={hideVillageMarkers} onCheckedChange={setHideVillageMarkers} />
                </div>
              </div>
            </div>

            {/* 筛选控制 */}
            <div className="p-6 border-b border-gray-200">
              <h3 className="font-semibold mb-3 flex items-center">
                <Filter className="w-4 h-4 mr-2" />
                筛选控制
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm mb-2 block">物种分类</label>
                  <Select value={selectedSpecies} onValueChange={setSelectedSpecies}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {speciesCategories.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm mb-2 block">时间范围</label>
                  <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部时间</SelectItem>
                      <SelectItem value="recent">最近一年</SelectItem>
                      <SelectItem value="2023">2023年</SelectItem>
                      <SelectItem value="2022">2022年</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* 统计面板 */}
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold mb-4 marine-primary">平台统计</h3>
              <div className="grid grid-cols-2 gap-4">
                <Card className="statistics-card">
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-red-600 mb-1">{stats.species.toLocaleString()}</div>
                    <div className="text-sm text-gray-600">物种总数</div>
                  </CardContent>
                </Card>

                <Card className="statistics-card">
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600 mb-1">{stats.recordings.toLocaleString()}</div>
                    <div className="text-sm text-gray-600">录音数量</div>
                  </CardContent>
                </Card>

                <Card className="statistics-card">
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-orange-600 mb-1">{stats.hotspots}</div>
                    <div className="text-sm text-gray-600">活跃热点</div>
                  </CardContent>
                </Card>

                <Card className="statistics-card">
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">{stats.onlineUsers}</div>
                    <div className="text-sm text-gray-600">在线用户</div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* 图例面板 */}
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold mb-4 marine-primary">图例说明</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-red-600 border"></div>
                  <span className="text-sm">极高密度区域 &gt;600种</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-orange-500 border"></div>
                  <span className="text-sm">高密度区域 400-600种</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-yellow-500 border"></div>
                  <span className="text-sm">中密度区域 200-400种</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-green-500 border"></div>
                  <span className="text-sm">低密度区域 100-200种</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-cyan-500 border"></div>
                  <span className="text-sm">稀少区域 &lt;100种</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">采集点</span>
                </div>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold mb-4 marine-primary">快速操作</h3>
              <div className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start bg-transparent"
                  onClick={() => (window.location.href = "/species")}
                >
                  <Search className="w-4 h-4 mr-2" />
                  高级搜索
                </Button>
                <Button variant="outline" className="w-full justify-start bg-transparent">
                  <Volume2 className="w-4 h-4 mr-2" />
                  热门录音
                </Button>
                <Button variant="outline" className="w-full justify-start bg-transparent">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  数据报告
                </Button>
              </div>
            </div>

            {/* 最新动态 */}
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4 marine-primary">最新动态</h3>
              <div className="space-y-3">
                <div className="text-sm">
                  <div className="font-medium">新增物种记录</div>
                  <div className="text-gray-600">太平洋蓝鲸新录音 - 2小时前</div>
                </div>
                <div className="text-sm">
                  <div className="font-medium">数据更新</div>
                  <div className="text-gray-600">南海区域数据同步完成 - 4小时前</div>
                </div>
                <div className="text-sm">
                  <div className="font-medium">系统维护</div>
                  <div className="text-gray-600">定期维护已完成 - 1天前</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
