'use client';

import React, { useState } from 'react';
import { MapLayerType, GridCell } from '@/lib/hotspot-types';
import HotspotLegend from '@/components/hotspot-legend';
import MapControls from '@/components/map-controls';
import InfoPanel from '@/components/info-panel';
import SearchBar from '@/components/search-bar';

export default function TestHotspotsPage() {
  const [layerType, setLayerType] = useState<MapLayerType>('street');
  const [hoveredCell, setHoveredCell] = useState<GridCell | null>(null);

  // 模拟数据
  const mockCell: GridCell = {
    id: 'test-cell',
    bounds: { north: 40.0, south: 39.9, east: 116.5, west: 116.4 },
    center: { lat: 39.95, lng: 116.45 },
    hotspotCount: 125,
    color: '#FD8D3C',
  };

  const handleLocationSelect = (location: { lat: number; lng: number; name: string }) => {
    console.log('Selected location:', location);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">eBird 热点地图测试</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 搜索栏 */}
          <div className="lg:col-span-4">
            <SearchBar 
              onLocationSelect={handleLocationSelect}
              placeholder="搜索地点..."
            />
          </div>

          {/* 地图区域占位 */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-lg p-8 h-96 flex items-center justify-center">
              <div className="text-center">
                <div className="text-6xl mb-4">🗺️</div>
                <h2 className="text-xl font-semibold text-gray-700 mb-2">地图区域</h2>
                <p className="text-gray-500">这里将显示交互式热点地图</p>
                <div className="mt-4 grid grid-cols-8 gap-1">
                  {Array.from({ length: 64 }, (_, i) => (
                    <div
                      key={i}
                      className="w-4 h-4 rounded-sm"
                      style={{
                        backgroundColor: i % 8 === 0 ? '#800026' :
                                       i % 7 === 0 ? '#BD0026' :
                                       i % 6 === 0 ? '#E31A1C' :
                                       i % 5 === 0 ? '#FC4E2A' :
                                       i % 4 === 0 ? '#FD8D3C' :
                                       i % 3 === 0 ? '#FEB24C' :
                                       i % 2 === 0 ? '#FED976' : '#FFEDA0'
                      }}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 右侧控件 */}
          <div className="space-y-6">
            {/* 信息面板 */}
            <InfoPanel
              hoveredCell={hoveredCell}
              totalHotspots={12847}
              visibleHotspots={3421}
              currentZoom={8}
            />

            {/* 图例 */}
            <HotspotLegend />

            {/* 地图控件 */}
            <MapControls
              currentLayer={layerType}
              onLayerChange={setLayerType}
            />

            {/* 测试按钮 */}
            <div className="bg-white rounded-lg shadow-lg p-4">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">测试控件</h3>
              <div className="space-y-2">
                <button
                  onClick={() => setHoveredCell(mockCell)}
                  className="w-full px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                >
                  模拟悬停网格
                </button>
                <button
                  onClick={() => setHoveredCell(null)}
                  className="w-full px-3 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
                >
                  清除悬停
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 功能说明 */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">eBird 热点地图功能</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-700">🎯 网格化热点显示</h3>
              <p className="text-sm text-gray-600">
                地图被分割成规则网格，每个网格根据包含的热点数量显示不同颜色
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-700">🎨 颜色分级系统</h3>
              <p className="text-sm text-gray-600">
                从浅黄色到深红色的8级颜色分级，直观显示热点密度
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-700">🔍 交互式搜索</h3>
              <p className="text-sm text-gray-600">
                支持地点名称搜索，快速定位到感兴趣的区域
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-700">🗺️ 多种地图类型</h3>
              <p className="text-sm text-gray-600">
                支持街道图、卫星图、地形图和混合图等多种底图
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-700">📊 实时信息显示</h3>
              <p className="text-sm text-gray-600">
                鼠标悬停时显示网格详细信息，包括坐标和热点数量
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-700">📱 响应式设计</h3>
              <p className="text-sm text-gray-600">
                适配不同设备屏幕，提供一致的用户体验
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
