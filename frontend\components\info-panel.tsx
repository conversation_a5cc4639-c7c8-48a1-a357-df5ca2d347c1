'use client';

import React from 'react';
import { GridCell } from '@/lib/hotspot-types';
import { formatCount, getGridSizeByZoom } from '@/lib/hotspot-utils';
import { MapPin, Activity } from 'lucide-react';

interface InfoPanelProps {
  hoveredCell: GridCell | null;
  totalHotspots: number;
  visibleHotspots: number;
  currentZoom?: number;
  className?: string;
}

export function InfoPanel({
  hoveredCell,
  totalHotspots,
  visibleHotspots,
  currentZoom = 8,
  className = ''
}: InfoPanelProps) {
  return (
    <div className={`bg-white rounded-lg shadow-lg p-4 ${className}`}>
      <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
        <Activity className="h-4 w-4 mr-2" />
        热点信息
      </h3>
      
      {hoveredCell ? (
        <div className="space-y-3">
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="font-medium text-blue-900 mb-1">
                  网格区域
                </div>
                <div className="text-sm text-blue-700 space-y-1">
                  <div className="flex items-center">
                    <MapPin className="h-3 w-3 mr-1" />
                    中心: {hoveredCell.center.lat.toFixed(4)}, {hoveredCell.center.lng.toFixed(4)}
                  </div>
                  <div>
                    范围: {hoveredCell.bounds.south.toFixed(4)} ~ {hoveredCell.bounds.north.toFixed(4)}°N
                  </div>
                  <div className="ml-6">
                    {hoveredCell.bounds.west.toFixed(4)} ~ {hoveredCell.bounds.east.toFixed(4)}°E
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 gap-2">
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <span className="text-sm text-gray-600">热点数量</span>
              <span className="font-semibold text-lg text-gray-900">
                {formatCount(hoveredCell.hotspotCount)}
              </span>
            </div>
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <span className="text-sm text-gray-600">颜色等级</span>
              <div className="flex items-center space-x-2">
                <div 
                  className="w-4 h-4 rounded border border-gray-300"
                  style={{ backgroundColor: hoveredCell.color }}
                />
                <span className="text-sm font-medium text-gray-700">
                  {hoveredCell.hotspotCount > 500 ? '500+' : 
                   hoveredCell.hotspotCount > 300 ? '301-500' :
                   hoveredCell.hotspotCount > 200 ? '201-300' :
                   hoveredCell.hotspotCount > 150 ? '151-200' :
                   hoveredCell.hotspotCount > 100 ? '101-150' :
                   hoveredCell.hotspotCount > 50 ? '51-100' :
                   hoveredCell.hotspotCount > 10 ? '11-50' : '0-10'}
                </span>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-3">
          <div className="text-center py-6 text-gray-500">
            <MapPin className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">将鼠标悬停在网格上</p>
            <p className="text-xs">查看详细信息</p>
          </div>
          
          <div className="pt-3 border-t border-gray-200">
            <div className="grid grid-cols-1 gap-2">
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm text-gray-600">总热点数</span>
                <span className="font-semibold text-gray-900">
                  {formatCount(totalHotspots)}
                </span>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm text-gray-600">可见热点</span>
                <span className="font-semibold text-gray-900">
                  {formatCount(visibleHotspots)}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="text-xs text-gray-500 space-y-1">
          <p>• 网格大小: {getGridSizeByZoom(currentZoom)}° × {getGridSizeByZoom(currentZoom)}°</p>
          <p>• 缩放级别: {currentZoom}</p>
          <p>• 数据更新: 实时</p>
          <p>• 点击网格可查看详情</p>
        </div>
      </div>
    </div>
  );
}

export default InfoPanel;
