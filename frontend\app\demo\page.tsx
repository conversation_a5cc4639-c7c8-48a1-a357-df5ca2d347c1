'use client';

import React from 'react';
import Link from 'next/link';
import { MapP<PERSON>, Bird, Search, Layers, BarChart3, Zap } from 'lucide-react';

export default function DemoPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center space-x-3">
            <Bird className="h-10 w-10 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">eBird 热点地图复刻</h1>
              <p className="text-sm text-gray-600">完美复刻 eBird 官网热点地图功能</p>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* 介绍部分 */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            发现最佳观鸟地点
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            基于 eBird 数据的交互式热点地图，帮助您找到最活跃的观鸟区域。
            使用网格化显示和颜色分级系统，直观展示全球观鸟热点分布。
          </p>
        </div>

        {/* 功能卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <MapPin className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">网格化热点</h3>
            <p className="text-gray-600">
              地图被分割成规则网格，每个网格根据热点数量显示不同颜色，清晰展示观鸟活动密度。
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <Search className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">智能搜索</h3>
            <p className="text-gray-600">
              支持地点名称搜索，自动补全功能，快速定位到您感兴趣的观鸟区域。
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <Layers className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">多种地图</h3>
            <p className="text-gray-600">
              支持街道图、卫星图、地形图等多种底图，满足不同用户的查看需求。
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
              <BarChart3 className="h-6 w-6 text-orange-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">实时统计</h3>
            <p className="text-gray-600">
              显示热点数量统计，网格详细信息，帮助您了解观鸟活动的具体情况。
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
              <Zap className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">交互体验</h3>
            <p className="text-gray-600">
              鼠标悬停显示详情，平滑的动画效果，提供流畅的用户交互体验。
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
              <Bird className="h-6 w-6 text-indigo-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">完美复刻</h3>
            <p className="text-gray-600">
              高度还原 eBird 官网的界面设计和交互逻辑，提供原汁原味的使用体验。
            </p>
          </div>
        </div>

        {/* 行动按钮 */}
        <div className="text-center space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/hotspots"
              className="inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg"
            >
              <MapPin className="h-5 w-5 mr-2" />
              查看完整热点地图
            </Link>
            <Link
              href="/test-hotspots"
              className="inline-flex items-center px-8 py-4 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors shadow-lg"
            >
              <Search className="h-5 w-5 mr-2" />
              查看功能演示
            </Link>
          </div>
          <p className="text-sm text-gray-500">
            点击上方按钮体验我们的热点地图功能
          </p>
        </div>

        {/* 技术说明 */}
        <div className="mt-16 bg-white rounded-xl shadow-lg p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">技术实现</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-semibold text-gray-800 mb-3">前端技术</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• React 19 + Next.js 15</li>
                <li>• TypeScript 类型安全</li>
                <li>• Tailwind CSS 样式框架</li>
                <li>• Leaflet 地图库</li>
                <li>• 响应式设计</li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gray-800 mb-3">核心功能</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• 网格化数据可视化</li>
                <li>• 8级颜色分级系统</li>
                <li>• 动态数据生成</li>
                <li>• 交互式用户界面</li>
                <li>• 高性能渲染优化</li>
              </ul>
            </div>
          </div>
        </div>
      </main>

      {/* 页脚 */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>© 2025 eBird 热点地图复刻项目 - 基于现代 Web 技术构建</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
