/**
 * 海外主要城市数据
 * 用于在地图上显示中文城市标注
 */

export interface OverseasCity {
  id: string
  name: string // 中文名称
  nameEn: string // 英文名称
  country: string // 国家
  countryCode: string // 国家代码
  lat: number
  lng: number
  population?: number // 人口（可选）
  isCapital?: boolean // 是否为首都
  region: string // 所属地理区域
  minZoom?: number // 最小显示缩放级别
}

export const overseasCities: OverseasCity[] = [
  // 日本主要城市
  {
    id: "tokyo",
    name: "东京",
    nameEn: "Tokyo",
    country: "日本",
    countryCode: "JP",
    lat: 35.6762,
    lng: 139.6503,
    population: 13960000,
    isCapital: true,
    region: "japan",
    minZoom: 4
  },
  {
    id: "osaka",
    name: "大阪",
    nameEn: "Osaka",
    country: "日本",
    countryCode: "JP",
    lat: 34.6937,
    lng: 135.5023,
    population: 2690000,
    region: "japan",
    minZoom: 5
  },
  {
    id: "kyoto",
    name: "京都",
    nameEn: "Kyoto",
    country: "日本",
    countryCode: "JP",
    lat: 35.0116,
    lng: 135.7681,
    population: 1460000,
    region: "japan",
    minZoom: 6
  },
  {
    id: "yokohama",
    name: "横滨",
    nameEn: "Yokohama",
    country: "日本",
    countryCode: "JP",
    lat: 35.4437,
    lng: 139.6380,
    population: 3750000,
    region: "japan",
    minZoom: 6
  },
  {
    id: "nagoya",
    name: "名古屋",
    nameEn: "Nagoya",
    country: "日本",
    countryCode: "JP",
    lat: 35.1815,
    lng: 136.9066,
    population: 2320000,
    region: "japan",
    minZoom: 6
  },

  // 韩国主要城市
  {
    id: "seoul",
    name: "首尔",
    nameEn: "Seoul",
    country: "韩国",
    countryCode: "KR",
    lat: 37.5665,
    lng: 126.9780,
    population: 9720000,
    isCapital: true,
    region: "south-korea",
    minZoom: 4
  },
  {
    id: "busan",
    name: "釜山",
    nameEn: "Busan",
    country: "韩国",
    countryCode: "KR",
    lat: 35.1796,
    lng: 129.0756,
    population: 3450000,
    region: "south-korea",
    minZoom: 5
  },
  {
    id: "incheon",
    name: "仁川",
    nameEn: "Incheon",
    country: "韩国",
    countryCode: "KR",
    lat: 37.4563,
    lng: 126.7052,
    population: 2950000,
    region: "south-korea",
    minZoom: 6
  },
  {
    id: "daegu",
    name: "大邱",
    nameEn: "Daegu",
    country: "韩国",
    countryCode: "KR",
    lat: 35.8714,
    lng: 128.6014,
    population: 2440000,
    region: "south-korea",
    minZoom: 6
  },

  // 台湾主要城市
  {
    id: "taipei",
    name: "台北",
    nameEn: "Taipei",
    country: "中国台湾",
    countryCode: "TW",
    lat: 25.0330,
    lng: 121.5654,
    population: 2650000,
    isCapital: true,
    region: "china-taiwan",
    minZoom: 5
  },
  {
    id: "kaohsiung",
    name: "高雄",
    nameEn: "Kaohsiung",
    country: "中国台湾",
    countryCode: "TW",
    lat: 22.6273,
    lng: 120.3014,
    population: 2770000,
    region: "china-taiwan",
    minZoom: 6
  },
  {
    id: "taichung",
    name: "台中",
    nameEn: "Taichung",
    country: "中国台湾",
    countryCode: "TW",
    lat: 24.1477,
    lng: 120.6736,
    population: 2820000,
    region: "china-taiwan",
    minZoom: 6
  },

  // 东南亚主要城市
  {
    id: "singapore",
    name: "新加坡",
    nameEn: "Singapore",
    country: "新加坡",
    countryCode: "SG",
    lat: 1.3521,
    lng: 103.8198,
    population: 5850000,
    isCapital: true,
    region: "southeast-asia",
    minZoom: 4
  },
  {
    id: "bangkok",
    name: "曼谷",
    nameEn: "Bangkok",
    country: "泰国",
    countryCode: "TH",
    lat: 13.7563,
    lng: 100.5018,
    population: 10540000,
    isCapital: true,
    region: "southeast-asia",
    minZoom: 4
  },
  {
    id: "kuala_lumpur",
    name: "吉隆坡",
    nameEn: "Kuala Lumpur",
    country: "马来西亚",
    countryCode: "MY",
    lat: 3.1390,
    lng: 101.6869,
    population: 1780000,
    isCapital: true,
    region: "southeast-asia",
    minZoom: 5
  },
  {
    id: "jakarta",
    name: "雅加达",
    nameEn: "Jakarta",
    country: "印度尼西亚",
    countryCode: "ID",
    lat: -6.2088,
    lng: 106.8456,
    population: 10560000,
    isCapital: true,
    region: "southeast-asia",
    minZoom: 4
  },
  {
    id: "manila",
    name: "马尼拉",
    nameEn: "Manila",
    country: "菲律宾",
    countryCode: "PH",
    lat: 14.5995,
    lng: 120.9842,
    population: 13480000,
    isCapital: true,
    region: "southeast-asia",
    minZoom: 4
  },
  {
    id: "ho_chi_minh",
    name: "胡志明市",
    nameEn: "Ho Chi Minh City",
    country: "越南",
    countryCode: "VN",
    lat: 10.8231,
    lng: 106.6297,
    population: 9000000,
    region: "southeast-asia",
    minZoom: 5
  },

  // 欧洲主要城市
  {
    id: "london",
    name: "伦敦",
    nameEn: "London",
    country: "英国",
    countryCode: "GB",
    lat: 51.5074,
    lng: -0.1278,
    population: 9540000,
    isCapital: true,
    region: "europe",
    minZoom: 4
  },
  {
    id: "paris",
    name: "巴黎",
    nameEn: "Paris",
    country: "法国",
    countryCode: "FR",
    lat: 48.8566,
    lng: 2.3522,
    population: 11020000,
    isCapital: true,
    region: "europe",
    minZoom: 4
  },
  {
    id: "berlin",
    name: "柏林",
    nameEn: "Berlin",
    country: "德国",
    countryCode: "DE",
    lat: 52.5200,
    lng: 13.4050,
    population: 3670000,
    isCapital: true,
    region: "europe",
    minZoom: 5
  },
  {
    id: "rome",
    name: "罗马",
    nameEn: "Rome",
    country: "意大利",
    countryCode: "IT",
    lat: 41.9028,
    lng: 12.4964,
    population: 2870000,
    isCapital: true,
    region: "europe",
    minZoom: 5
  },

  // 北美主要城市
  {
    id: "new_york",
    name: "纽约",
    nameEn: "New York",
    country: "美国",
    countryCode: "US",
    lat: 40.7128,
    lng: -74.0060,
    population: 8400000,
    region: "north-america",
    minZoom: 4
  },
  {
    id: "los_angeles",
    name: "洛杉矶",
    nameEn: "Los Angeles",
    country: "美国",
    countryCode: "US",
    lat: 34.0522,
    lng: -118.2437,
    population: 3970000,
    region: "north-america",
    minZoom: 4
  },
  {
    id: "toronto",
    name: "多伦多",
    nameEn: "Toronto",
    country: "加拿大",
    countryCode: "CA",
    lat: 43.6532,
    lng: -79.3832,
    population: 2930000,
    region: "north-america",
    minZoom: 5
  },
  {
    id: "vancouver",
    name: "温哥华",
    nameEn: "Vancouver",
    country: "加拿大",
    countryCode: "CA",
    lat: 49.2827,
    lng: -123.1207,
    population: 2580000,
    region: "north-america",
    minZoom: 5
  }
]

/**
 * 根据区域获取城市列表
 */
export function getCitiesByRegion(region: string): OverseasCity[] {
  return overseasCities.filter(city => city.region === region)
}

/**
 * 根据缩放级别获取应该显示的城市
 */
export function getCitiesByZoomLevel(region: string, zoomLevel: number): OverseasCity[] {
  return getCitiesByRegion(region).filter(city => 
    !city.minZoom || zoomLevel >= city.minZoom
  )
}

/**
 * 获取首都城市
 */
export function getCapitalCities(): OverseasCity[] {
  return overseasCities.filter(city => city.isCapital)
}

/**
 * 根据人口排序获取主要城市
 */
export function getMajorCities(limit: number = 20): OverseasCity[] {
  return overseasCities
    .filter(city => city.population)
    .sort((a, b) => (b.population || 0) - (a.population || 0))
    .slice(0, limit)
}
