/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/test-hotspots/page"],{

/***/ "(app-pages-browser)/./app/test-hotspots/page.tsx":
/*!************************************!*\
  !*** ./app/test-hotspots/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestHotspotsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_hotspot_legend__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/hotspot-legend */ \"(app-pages-browser)/./components/hotspot-legend.tsx\");\n/* harmony import */ var _components_map_controls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/map-controls */ \"(app-pages-browser)/./components/map-controls.tsx\");\n/* harmony import */ var _components_info_panel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/info-panel */ \"(app-pages-browser)/./components/info-panel.tsx\");\n/* harmony import */ var _components_search_bar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/search-bar */ \"(app-pages-browser)/./components/search-bar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TestHotspotsPage() {\n    _s();\n    const [layerType, setLayerType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('street');\n    const [hoveredCell, setHoveredCell] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 模拟数据\n    const mockCell = {\n        id: 'test-cell',\n        bounds: {\n            north: 40.0,\n            south: 39.9,\n            east: 116.5,\n            west: 116.4\n        },\n        center: {\n            lat: 39.95,\n            lng: 116.45\n        },\n        hotspotCount: 125,\n        color: '#FD8D3C'\n    };\n    const handleLocationSelect = (location)=>{\n        console.log('Selected location:', location);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold text-gray-900 mb-6\",\n                    children: \"eBird 热点地图测试\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_search_bar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                onLocationSelect: handleLocationSelect,\n                                placeholder: \"搜索地点...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-8 h-96 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl mb-4\",\n                                            children: \"\\uD83D\\uDDFA️\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                            children: \"地图区域\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"这里将显示交互式热点地图\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-8 gap-1\",\n                                            children: Array.from({\n                                                length: 64\n                                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded-sm\",\n                                                    style: {\n                                                        backgroundColor: i % 8 === 0 ? '#800026' : i % 7 === 0 ? '#BD0026' : i % 6 === 0 ? '#E31A1C' : i % 5 === 0 ? '#FC4E2A' : i % 4 === 0 ? '#FD8D3C' : i % 3 === 0 ? '#FEB24C' : i % 2 === 0 ? '#FED976' : '#FFEDA0'\n                                                    }\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_info_panel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    hoveredCell: hoveredCell,\n                                    totalHotspots: 12847,\n                                    visibleHotspots: 3421\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hotspot_legend__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_controls__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    currentLayer: layerType,\n                                    onLayerChange: setLayerType\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-700 mb-3\",\n                                            children: \"测试控件\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setHoveredCell(mockCell),\n                                                    className: \"w-full px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600\",\n                                                    children: \"模拟悬停网格\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setHoveredCell(null),\n                                                    className: \"w-full px-3 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600\",\n                                                    children: \"清除悬停\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"eBird 热点地图功能\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83C\\uDFAF 网格化热点显示\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"地图被分割成规则网格，每个网格根据包含的热点数量显示不同颜色\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83C\\uDFA8 颜色分级系统\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"从浅黄色到深红色的8级颜色分级，直观显示热点密度\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83D\\uDD0D 交互式搜索\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"支持地点名称搜索，快速定位到感兴趣的区域\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83D\\uDDFA️ 多种地图类型\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"支持街道图、卫星图、地形图和混合图等多种底图\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83D\\uDCCA 实时信息显示\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"鼠标悬停时显示网格详细信息，包括坐标和热点数量\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"\\uD83D\\uDCF1 响应式设计\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"适配不同设备屏幕，提供一致的用户体验\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\app\\\\test-hotspots\\\\page.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_s(TestHotspotsPage, \"lP7rQNDmTov8553k65gQD1QmQR0=\");\n_c = TestHotspotsPage;\nvar _c;\n$RefreshReg$(_c, \"TestHotspotsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-hotspots/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/hotspot-legend.tsx":
/*!***************************************!*\
  !*** ./components/hotspot-legend.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HotspotLegend: () => (/* binding */ HotspotLegend),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hotspot-utils */ \"(app-pages-browser)/./lib/hotspot-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ HotspotLegend,default auto */ \n\n\nfunction HotspotLegend(param) {\n    let { className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-4 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-semibold text-gray-700 mb-3\",\n                children: \"观察热点数量\"\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: _lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.COLOR_GRADES.map((grade, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 rounded-sm border border-gray-300\",\n                                style: {\n                                    backgroundColor: grade.color\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-600 font-medium\",\n                                children: grade.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-3 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"每个网格显示该区域内的热点数量\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\hotspot-legend.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_c = HotspotLegend;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HotspotLegend);\nvar _c;\n$RefreshReg$(_c, \"HotspotLegend\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/hotspot-legend.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/info-panel.tsx":
/*!***********************************!*\
  !*** ./components/info-panel.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfoPanel: () => (/* binding */ InfoPanel),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hotspot-utils */ \"(app-pages-browser)/./lib/hotspot-utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ InfoPanel,default auto */ \n\n\n\nfunction InfoPanel(param) {\n    let { hoveredCell, totalHotspots, visibleHotspots, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-4 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-semibold text-gray-700 mb-3 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    \"热点信息\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            hoveredCell ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-blue-50 rounded-lg border border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-blue-900 mb-1\",\n                                        children: \"网格区域\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-700 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"中心: \",\n                                                    hoveredCell.center.lat.toFixed(4),\n                                                    \", \",\n                                                    hoveredCell.center.lng.toFixed(4)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"范围: \",\n                                                    hoveredCell.bounds.south.toFixed(4),\n                                                    \" ~ \",\n                                                    hoveredCell.bounds.north.toFixed(4),\n                                                    \"\\xb0N\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6\",\n                                                children: [\n                                                    hoveredCell.bounds.west.toFixed(4),\n                                                    \" ~ \",\n                                                    hoveredCell.bounds.east.toFixed(4),\n                                                    \"\\xb0E\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"热点数量\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-lg text-gray-900\",\n                                        children: (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.formatCount)(hoveredCell.hotspotCount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"颜色等级\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 rounded border border-gray-300\",\n                                                style: {\n                                                    backgroundColor: hoveredCell.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: hoveredCell.hotspotCount > 500 ? '500+' : hoveredCell.hotspotCount > 300 ? '301-500' : hoveredCell.hotspotCount > 200 ? '201-300' : hoveredCell.hotspotCount > 150 ? '151-200' : hoveredCell.hotspotCount > 100 ? '101-150' : hoveredCell.hotspotCount > 50 ? '51-100' : hoveredCell.hotspotCount > 10 ? '11-50' : '0-10'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-6 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"将鼠标悬停在网格上\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: \"查看详细信息\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"总热点数\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.formatCount)(totalHotspots)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"可见热点\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: (0,_lib_hotspot_utils__WEBPACK_IMPORTED_MODULE_2__.formatCount)(visibleHotspots)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-3 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500 space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"• 网格大小: 0.1\\xb0 \\xd7 0.1\\xb0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"• 数据更新: 实时\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"• 点击网格可查看详情\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\info-panel.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_c = InfoPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InfoPanel);\nvar _c;\n$RefreshReg$(_c, \"InfoPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/info-panel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/map-controls.tsx":
/*!*************************************!*\
  !*** ./components/map-controls.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MapControls: () => (/* binding */ MapControls),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Layers,Map,Mountain,Satellite!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Layers,Map,Mountain,Satellite!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/satellite.js\");\n/* harmony import */ var _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Layers,Map,Mountain,Satellite!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mountain.js\");\n/* harmony import */ var _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Layers,Map,Mountain,Satellite!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* __next_internal_client_entry_do_not_use__ MapControls,default auto */ \n\n\nconst layerOptions = [\n    {\n        type: 'street',\n        label: '街道图',\n        icon: _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        description: '标准街道地图'\n    },\n    {\n        type: 'satellite',\n        label: '卫星图',\n        icon: _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        description: '卫星影像'\n    },\n    {\n        type: 'terrain',\n        label: '地形图',\n        icon: _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        description: '地形地貌'\n    },\n    {\n        type: 'hybrid',\n        label: '混合图',\n        icon: _barrel_optimize_names_Layers_Map_Mountain_Satellite_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        description: '卫星图+标注'\n    }\n];\nfunction MapControls(param) {\n    let { currentLayer, onLayerChange, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-4 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-semibold text-gray-700 mb-3\",\n                children: \"地图类型\"\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: layerOptions.map((option)=>{\n                    const IconComponent = option.icon;\n                    const isSelected = currentLayer === option.type;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onLayerChange(option.type),\n                        className: \"w-full flex items-center space-x-3 p-2 rounded-md text-left transition-colors\\n                        \".concat(isSelected ? 'bg-blue-50 text-blue-700 border border-blue-200' : 'hover:bg-gray-50 text-gray-700 border border-transparent'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-1 rounded \".concat(isSelected ? 'bg-blue-100' : 'bg-gray-100'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-sm\",\n                                        children: option.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: option.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 15\n                            }, this),\n                            isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, option.type, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-3 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-xs text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"网格工具\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"已启用\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map-controls.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_c = MapControls;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MapControls);\nvar _c;\n$RefreshReg$(_c, \"MapControls\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/map-controls.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/search-bar.tsx":
/*!***********************************!*\
  !*** ./components/search-bar.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: () => (/* binding */ SearchBar),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ SearchBar,default auto */ \nvar _s = $RefreshSig$();\n\n\n// 模拟搜索结果\nconst mockSearchResults = [\n    {\n        id: '1',\n        name: '北京市',\n        lat: 39.9042,\n        lng: 116.4074,\n        type: 'city'\n    },\n    {\n        id: '2',\n        name: '上海市',\n        lat: 31.2304,\n        lng: 121.4737,\n        type: 'city'\n    },\n    {\n        id: '3',\n        name: '广州市',\n        lat: 23.1291,\n        lng: 113.2644,\n        type: 'city'\n    },\n    {\n        id: '4',\n        name: '深圳市',\n        lat: 22.5431,\n        lng: 114.0579,\n        type: 'city'\n    },\n    {\n        id: '5',\n        name: '杭州市',\n        lat: 30.2741,\n        lng: 120.1551,\n        type: 'city'\n    },\n    {\n        id: '6',\n        name: '南京市',\n        lat: 32.0603,\n        lng: 118.7969,\n        type: 'city'\n    },\n    {\n        id: '7',\n        name: '成都市',\n        lat: 30.5728,\n        lng: 104.0668,\n        type: 'city'\n    },\n    {\n        id: '8',\n        name: '西安市',\n        lat: 34.3416,\n        lng: 108.9398,\n        type: 'city'\n    }\n];\nfunction SearchBar(param) {\n    let { onLocationSelect, placeholder = '输入地点名称或坐标...', className = '' } = param;\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const searchRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 搜索函数\n    const search = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SearchBar.useCallback[search]\": (searchQuery)=>{\n            if (!searchQuery.trim()) {\n                setResults([]);\n                return;\n            }\n            // 模拟搜索延迟\n            const filteredResults = mockSearchResults.filter({\n                \"SearchBar.useCallback[search].filteredResults\": (result)=>result.name.toLowerCase().includes(searchQuery.toLowerCase())\n            }[\"SearchBar.useCallback[search].filteredResults\"]);\n            setResults(filteredResults);\n        }\n    }[\"SearchBar.useCallback[search]\"], []);\n    // 处理输入变化\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setQuery(value);\n        search(value);\n        setIsOpen(true);\n        setSelectedIndex(-1);\n    };\n    // 处理选择结果\n    const handleSelectResult = (result)=>{\n        setQuery(result.name);\n        setIsOpen(false);\n        setResults([]);\n        onLocationSelect({\n            lat: result.lat,\n            lng: result.lng,\n            name: result.name\n        });\n    };\n    // 处理键盘事件\n    const handleKeyDown = (e)=>{\n        if (!isOpen || results.length === 0) return;\n        switch(e.key){\n            case 'ArrowDown':\n                e.preventDefault();\n                setSelectedIndex((prev)=>prev < results.length - 1 ? prev + 1 : prev);\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                setSelectedIndex((prev)=>prev > 0 ? prev - 1 : -1);\n                break;\n            case 'Enter':\n                e.preventDefault();\n                if (selectedIndex >= 0 && selectedIndex < results.length) {\n                    handleSelectResult(results[selectedIndex]);\n                }\n                break;\n            case 'Escape':\n                setIsOpen(false);\n                setSelectedIndex(-1);\n                break;\n        }\n    };\n    // 清除搜索\n    const clearSearch = ()=>{\n        var _inputRef_current;\n        setQuery('');\n        setResults([]);\n        setIsOpen(false);\n        setSelectedIndex(-1);\n        (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n    };\n    // 点击外部关闭\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchBar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"SearchBar.useEffect.handleClickOutside\": (event)=>{\n                    if (searchRef.current && !searchRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                        setSelectedIndex(-1);\n                    }\n                }\n            }[\"SearchBar.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"SearchBar.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"SearchBar.useEffect\"];\n        }\n    }[\"SearchBar.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: searchRef,\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"h-4 w-4 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: inputRef,\n                        type: \"text\",\n                        value: query,\n                        onChange: handleInputChange,\n                        onKeyDown: handleKeyDown,\n                        onFocus: ()=>query && setIsOpen(true),\n                        placeholder: placeholder,\n                        className: \"block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg  focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-sm placeholder-gray-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    query && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearSearch,\n                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4 text-gray-400 hover:text-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            isOpen && results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto\",\n                children: results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleSelectResult(result),\n                        className: \"w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3\\n                        \".concat(index === selectedIndex ? 'bg-blue-50 text-blue-700' : 'text-gray-700', \"\\n                        \").concat(index === 0 ? 'rounded-t-lg' : '', \"\\n                        \").concat(index === results.length - 1 ? 'rounded-b-lg' : 'border-b border-gray-100'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: result.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            result.lat.toFixed(4),\n                                            \", \",\n                                            result.lng.toFixed(4)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, result.id, true, {\n                        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\search-bar.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(SearchBar, \"NGCORj7r7lFC6MSTBNmxa0J1HKY=\");\n_c = SearchBar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchBar);\nvar _c;\n$RefreshReg$(_c, \"SearchBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/search-bar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/hotspot-utils.ts":
/*!******************************!*\
  !*** ./lib/hotspot-utils.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLOR_GRADES: () => (/* binding */ COLOR_GRADES),\n/* harmony export */   assignHotspotsToGrid: () => (/* binding */ assignHotspotsToGrid),\n/* harmony export */   calculateBounds: () => (/* binding */ calculateBounds),\n/* harmony export */   formatCount: () => (/* binding */ formatCount),\n/* harmony export */   generateGrid: () => (/* binding */ generateGrid),\n/* harmony export */   generateMockHotspots: () => (/* binding */ generateMockHotspots),\n/* harmony export */   getColorByCount: () => (/* binding */ getColorByCount)\n/* harmony export */ });\n// eBird 风格的颜色分级配置\nconst COLOR_GRADES = [\n    {\n        min: 0,\n        max: 10,\n        color: '#FFEDA0',\n        label: '0-10'\n    },\n    {\n        min: 11,\n        max: 50,\n        color: '#FED976',\n        label: '11-50'\n    },\n    {\n        min: 51,\n        max: 100,\n        color: '#FEB24C',\n        label: '51-100'\n    },\n    {\n        min: 101,\n        max: 150,\n        color: '#FD8D3C',\n        label: '101-150'\n    },\n    {\n        min: 151,\n        max: 200,\n        color: '#FC4E2A',\n        label: '151-200'\n    },\n    {\n        min: 201,\n        max: 300,\n        color: '#E31A1C',\n        label: '201-300'\n    },\n    {\n        min: 301,\n        max: 500,\n        color: '#BD0026',\n        label: '301-500'\n    },\n    {\n        min: 501,\n        max: Infinity,\n        color: '#800026',\n        label: '500+'\n    }\n];\n// 根据热点数量获取颜色\nfunction getColorByCount(count) {\n    const grade = COLOR_GRADES.find((g)=>count >= g.min && count <= g.max);\n    return (grade === null || grade === void 0 ? void 0 : grade.color) || COLOR_GRADES[0].color;\n}\n// 生成网格\nfunction generateGrid(bounds, gridSize) {\n    const cells = [];\n    // 计算网格数量\n    const latSteps = Math.ceil((bounds.north - bounds.south) / gridSize);\n    const lngSteps = Math.ceil((bounds.east - bounds.west) / gridSize);\n    for(let i = 0; i < latSteps; i++){\n        for(let j = 0; j < lngSteps; j++){\n            const south = bounds.south + i * gridSize;\n            const north = Math.min(bounds.north, south + gridSize);\n            const west = bounds.west + j * gridSize;\n            const east = Math.min(bounds.east, west + gridSize);\n            const cell = {\n                id: \"cell-\".concat(i, \"-\").concat(j),\n                bounds: {\n                    north,\n                    south,\n                    east,\n                    west\n                },\n                center: {\n                    lat: (north + south) / 2,\n                    lng: (east + west) / 2\n                },\n                hotspotCount: 0,\n                color: COLOR_GRADES[0].color\n            };\n            cells.push(cell);\n        }\n    }\n    return cells;\n}\n// 将热点数据分配到网格中\nfunction assignHotspotsToGrid(cells, hotspots) {\n    // 重置所有网格的热点数量\n    cells.forEach((cell)=>{\n        cell.hotspotCount = 0;\n    });\n    // 将热点分配到对应的网格\n    hotspots.forEach((hotspot)=>{\n        const cell = cells.find((c)=>hotspot.lat >= c.bounds.south && hotspot.lat < c.bounds.north && hotspot.lng >= c.bounds.west && hotspot.lng < c.bounds.east);\n        if (cell) {\n            cell.hotspotCount += hotspot.count;\n        }\n    });\n    // 更新网格颜色\n    cells.forEach((cell)=>{\n        cell.color = getColorByCount(cell.hotspotCount);\n    });\n    return cells;\n}\n// 生成模拟热点数据\nfunction generateMockHotspots(bounds, count) {\n    const hotspots = [];\n    for(let i = 0; i < count; i++){\n        const lat = bounds.south + Math.random() * (bounds.north - bounds.south);\n        const lng = bounds.west + Math.random() * (bounds.east - bounds.west);\n        const hotspotCount = Math.floor(Math.random() * 100) + 1;\n        hotspots.push({\n            id: \"hotspot-\".concat(i),\n            lat,\n            lng,\n            count: hotspotCount\n        });\n    }\n    return hotspots;\n}\n// 格式化数字显示\nfunction formatCount(count) {\n    if (count >= 1000) {\n        return \"\".concat((count / 1000).toFixed(1), \"k\");\n    }\n    return count.toString();\n}\n// 计算地图边界\nfunction calculateBounds(center, zoom) {\n    // 根据缩放级别计算合适的边界\n    const latRange = 180 / Math.pow(2, zoom - 1);\n    const lngRange = 360 / Math.pow(2, zoom - 1);\n    return {\n        north: center[0] + latRange / 2,\n        south: center[0] - latRange / 2,\n        east: center[1] + lngRange / 2,\n        west: center[1] - lngRange / 2\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9ob3RzcG90LXV0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFQSxrQkFBa0I7QUFDWCxNQUFNQSxlQUE2QjtJQUN4QztRQUFFQyxLQUFLO1FBQUdDLEtBQUs7UUFBSUMsT0FBTztRQUFXQyxPQUFPO0lBQU87SUFDbkQ7UUFBRUgsS0FBSztRQUFJQyxLQUFLO1FBQUlDLE9BQU87UUFBV0MsT0FBTztJQUFRO0lBQ3JEO1FBQUVILEtBQUs7UUFBSUMsS0FBSztRQUFLQyxPQUFPO1FBQVdDLE9BQU87SUFBUztJQUN2RDtRQUFFSCxLQUFLO1FBQUtDLEtBQUs7UUFBS0MsT0FBTztRQUFXQyxPQUFPO0lBQVU7SUFDekQ7UUFBRUgsS0FBSztRQUFLQyxLQUFLO1FBQUtDLE9BQU87UUFBV0MsT0FBTztJQUFVO0lBQ3pEO1FBQUVILEtBQUs7UUFBS0MsS0FBSztRQUFLQyxPQUFPO1FBQVdDLE9BQU87SUFBVTtJQUN6RDtRQUFFSCxLQUFLO1FBQUtDLEtBQUs7UUFBS0MsT0FBTztRQUFXQyxPQUFPO0lBQVU7SUFDekQ7UUFBRUgsS0FBSztRQUFLQyxLQUFLRztRQUFVRixPQUFPO1FBQVdDLE9BQU87SUFBTztDQUM1RCxDQUFDO0FBRUYsYUFBYTtBQUNOLFNBQVNFLGdCQUFnQkMsS0FBYTtJQUMzQyxNQUFNQyxRQUFRUixhQUFhUyxJQUFJLENBQUNDLENBQUFBLElBQUtILFNBQVNHLEVBQUVULEdBQUcsSUFBSU0sU0FBU0csRUFBRVIsR0FBRztJQUNyRSxPQUFPTSxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9MLEtBQUssS0FBSUgsWUFBWSxDQUFDLEVBQUUsQ0FBQ0csS0FBSztBQUM5QztBQUVBLE9BQU87QUFDQSxTQUFTUSxhQUFhQyxNQUFpQixFQUFFQyxRQUFnQjtJQUM5RCxNQUFNQyxRQUFvQixFQUFFO0lBRTVCLFNBQVM7SUFDVCxNQUFNQyxXQUFXQyxLQUFLQyxJQUFJLENBQUMsQ0FBQ0wsT0FBT00sS0FBSyxHQUFHTixPQUFPTyxLQUFLLElBQUlOO0lBQzNELE1BQU1PLFdBQVdKLEtBQUtDLElBQUksQ0FBQyxDQUFDTCxPQUFPUyxJQUFJLEdBQUdULE9BQU9VLElBQUksSUFBSVQ7SUFFekQsSUFBSyxJQUFJVSxJQUFJLEdBQUdBLElBQUlSLFVBQVVRLElBQUs7UUFDakMsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlKLFVBQVVJLElBQUs7WUFDakMsTUFBTUwsUUFBUVAsT0FBT08sS0FBSyxHQUFHSSxJQUFJVjtZQUNqQyxNQUFNSyxRQUFRRixLQUFLZixHQUFHLENBQUNXLE9BQU9NLEtBQUssRUFBRUMsUUFBUU47WUFDN0MsTUFBTVMsT0FBT1YsT0FBT1UsSUFBSSxHQUFHRSxJQUFJWDtZQUMvQixNQUFNUSxPQUFPTCxLQUFLZixHQUFHLENBQUNXLE9BQU9TLElBQUksRUFBRUMsT0FBT1Q7WUFFMUMsTUFBTVksT0FBaUI7Z0JBQ3JCQyxJQUFJLFFBQWFGLE9BQUxELEdBQUUsS0FBSyxPQUFGQztnQkFDakJaLFFBQVE7b0JBQUVNO29CQUFPQztvQkFBT0U7b0JBQU1DO2dCQUFLO2dCQUNuQ0ssUUFBUTtvQkFDTkMsS0FBSyxDQUFDVixRQUFRQyxLQUFJLElBQUs7b0JBQ3ZCVSxLQUFLLENBQUNSLE9BQU9DLElBQUcsSUFBSztnQkFDdkI7Z0JBQ0FRLGNBQWM7Z0JBQ2QzQixPQUFPSCxZQUFZLENBQUMsRUFBRSxDQUFDRyxLQUFLO1lBQzlCO1lBRUFXLE1BQU1pQixJQUFJLENBQUNOO1FBQ2I7SUFDRjtJQUVBLE9BQU9YO0FBQ1Q7QUFFQSxjQUFjO0FBQ1AsU0FBU2tCLHFCQUNkbEIsS0FBaUIsRUFDakJtQixRQUF1QjtJQUV2QixjQUFjO0lBQ2RuQixNQUFNb0IsT0FBTyxDQUFDVCxDQUFBQTtRQUNaQSxLQUFLSyxZQUFZLEdBQUc7SUFDdEI7SUFFQSxjQUFjO0lBQ2RHLFNBQVNDLE9BQU8sQ0FBQ0MsQ0FBQUE7UUFDZixNQUFNVixPQUFPWCxNQUFNTCxJQUFJLENBQUMyQixDQUFBQSxJQUN0QkQsUUFBUVAsR0FBRyxJQUFJUSxFQUFFeEIsTUFBTSxDQUFDTyxLQUFLLElBQzdCZ0IsUUFBUVAsR0FBRyxHQUFHUSxFQUFFeEIsTUFBTSxDQUFDTSxLQUFLLElBQzVCaUIsUUFBUU4sR0FBRyxJQUFJTyxFQUFFeEIsTUFBTSxDQUFDVSxJQUFJLElBQzVCYSxRQUFRTixHQUFHLEdBQUdPLEVBQUV4QixNQUFNLENBQUNTLElBQUk7UUFHN0IsSUFBSUksTUFBTTtZQUNSQSxLQUFLSyxZQUFZLElBQUlLLFFBQVE1QixLQUFLO1FBQ3BDO0lBQ0Y7SUFFQSxTQUFTO0lBQ1RPLE1BQU1vQixPQUFPLENBQUNULENBQUFBO1FBQ1pBLEtBQUt0QixLQUFLLEdBQUdHLGdCQUFnQm1CLEtBQUtLLFlBQVk7SUFDaEQ7SUFFQSxPQUFPaEI7QUFDVDtBQUVBLFdBQVc7QUFDSixTQUFTdUIscUJBQXFCekIsTUFBaUIsRUFBRUwsS0FBYTtJQUNuRSxNQUFNMEIsV0FBMEIsRUFBRTtJQUVsQyxJQUFLLElBQUlWLElBQUksR0FBR0EsSUFBSWhCLE9BQU9nQixJQUFLO1FBQzlCLE1BQU1LLE1BQU1oQixPQUFPTyxLQUFLLEdBQUdILEtBQUtzQixNQUFNLEtBQU0xQixDQUFBQSxPQUFPTSxLQUFLLEdBQUdOLE9BQU9PLEtBQUs7UUFDdkUsTUFBTVUsTUFBTWpCLE9BQU9VLElBQUksR0FBR04sS0FBS3NCLE1BQU0sS0FBTTFCLENBQUFBLE9BQU9TLElBQUksR0FBR1QsT0FBT1UsSUFBSTtRQUNwRSxNQUFNUSxlQUFlZCxLQUFLdUIsS0FBSyxDQUFDdkIsS0FBS3NCLE1BQU0sS0FBSyxPQUFPO1FBRXZETCxTQUFTRixJQUFJLENBQUM7WUFDWkwsSUFBSSxXQUFhLE9BQUZIO1lBQ2ZLO1lBQ0FDO1lBQ0F0QixPQUFPdUI7UUFDVDtJQUNGO0lBRUEsT0FBT0c7QUFDVDtBQUVBLFVBQVU7QUFDSCxTQUFTTyxZQUFZakMsS0FBYTtJQUN2QyxJQUFJQSxTQUFTLE1BQU07UUFDakIsT0FBTyxHQUE2QixPQUExQixDQUFDQSxRQUFRLElBQUcsRUFBR2tDLE9BQU8sQ0FBQyxJQUFHO0lBQ3RDO0lBQ0EsT0FBT2xDLE1BQU1tQyxRQUFRO0FBQ3ZCO0FBRUEsU0FBUztBQUNGLFNBQVNDLGdCQUFnQmhCLE1BQXdCLEVBQUVpQixJQUFZO0lBQ3BFLGdCQUFnQjtJQUNoQixNQUFNQyxXQUFXLE1BQU03QixLQUFLOEIsR0FBRyxDQUFDLEdBQUdGLE9BQU87SUFDMUMsTUFBTUcsV0FBVyxNQUFNL0IsS0FBSzhCLEdBQUcsQ0FBQyxHQUFHRixPQUFPO0lBRTFDLE9BQU87UUFDTDFCLE9BQU9TLE1BQU0sQ0FBQyxFQUFFLEdBQUdrQixXQUFXO1FBQzlCMUIsT0FBT1EsTUFBTSxDQUFDLEVBQUUsR0FBR2tCLFdBQVc7UUFDOUJ4QixNQUFNTSxNQUFNLENBQUMsRUFBRSxHQUFHb0IsV0FBVztRQUM3QnpCLE1BQU1LLE1BQU0sQ0FBQyxFQUFFLEdBQUdvQixXQUFXO0lBQy9CO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFx3b3Jrc3BhY2VzXFxkZFxcbWJkcFxcZnJvbnRlbmRcXGxpYlxcaG90c3BvdC11dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDb2xvckdyYWRlLCBHcmlkQ2VsbCwgTWFwQm91bmRzLCBIb3RzcG90RGF0YSB9IGZyb20gJy4vaG90c3BvdC10eXBlcyc7XG5cbi8vIGVCaXJkIOmjjuagvOeahOminOiJsuWIhue6p+mFjee9rlxuZXhwb3J0IGNvbnN0IENPTE9SX0dSQURFUzogQ29sb3JHcmFkZVtdID0gW1xuICB7IG1pbjogMCwgbWF4OiAxMCwgY29sb3I6ICcjRkZFREEwJywgbGFiZWw6ICcwLTEwJyB9LFxuICB7IG1pbjogMTEsIG1heDogNTAsIGNvbG9yOiAnI0ZFRDk3NicsIGxhYmVsOiAnMTEtNTAnIH0sXG4gIHsgbWluOiA1MSwgbWF4OiAxMDAsIGNvbG9yOiAnI0ZFQjI0QycsIGxhYmVsOiAnNTEtMTAwJyB9LFxuICB7IG1pbjogMTAxLCBtYXg6IDE1MCwgY29sb3I6ICcjRkQ4RDNDJywgbGFiZWw6ICcxMDEtMTUwJyB9LFxuICB7IG1pbjogMTUxLCBtYXg6IDIwMCwgY29sb3I6ICcjRkM0RTJBJywgbGFiZWw6ICcxNTEtMjAwJyB9LFxuICB7IG1pbjogMjAxLCBtYXg6IDMwMCwgY29sb3I6ICcjRTMxQTFDJywgbGFiZWw6ICcyMDEtMzAwJyB9LFxuICB7IG1pbjogMzAxLCBtYXg6IDUwMCwgY29sb3I6ICcjQkQwMDI2JywgbGFiZWw6ICczMDEtNTAwJyB9LFxuICB7IG1pbjogNTAxLCBtYXg6IEluZmluaXR5LCBjb2xvcjogJyM4MDAwMjYnLCBsYWJlbDogJzUwMCsnIH0sXG5dO1xuXG4vLyDmoLnmja7ng63ngrnmlbDph4/ojrflj5bpopzoibJcbmV4cG9ydCBmdW5jdGlvbiBnZXRDb2xvckJ5Q291bnQoY291bnQ6IG51bWJlcik6IHN0cmluZyB7XG4gIGNvbnN0IGdyYWRlID0gQ09MT1JfR1JBREVTLmZpbmQoZyA9PiBjb3VudCA+PSBnLm1pbiAmJiBjb3VudCA8PSBnLm1heCk7XG4gIHJldHVybiBncmFkZT8uY29sb3IgfHwgQ09MT1JfR1JBREVTWzBdLmNvbG9yO1xufVxuXG4vLyDnlJ/miJDnvZHmoLxcbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUdyaWQoYm91bmRzOiBNYXBCb3VuZHMsIGdyaWRTaXplOiBudW1iZXIpOiBHcmlkQ2VsbFtdIHtcbiAgY29uc3QgY2VsbHM6IEdyaWRDZWxsW10gPSBbXTtcbiAgXG4gIC8vIOiuoeeul+e9keagvOaVsOmHj1xuICBjb25zdCBsYXRTdGVwcyA9IE1hdGguY2VpbCgoYm91bmRzLm5vcnRoIC0gYm91bmRzLnNvdXRoKSAvIGdyaWRTaXplKTtcbiAgY29uc3QgbG5nU3RlcHMgPSBNYXRoLmNlaWwoKGJvdW5kcy5lYXN0IC0gYm91bmRzLndlc3QpIC8gZ3JpZFNpemUpO1xuICBcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBsYXRTdGVwczsgaSsrKSB7XG4gICAgZm9yIChsZXQgaiA9IDA7IGogPCBsbmdTdGVwczsgaisrKSB7XG4gICAgICBjb25zdCBzb3V0aCA9IGJvdW5kcy5zb3V0aCArIGkgKiBncmlkU2l6ZTtcbiAgICAgIGNvbnN0IG5vcnRoID0gTWF0aC5taW4oYm91bmRzLm5vcnRoLCBzb3V0aCArIGdyaWRTaXplKTtcbiAgICAgIGNvbnN0IHdlc3QgPSBib3VuZHMud2VzdCArIGogKiBncmlkU2l6ZTtcbiAgICAgIGNvbnN0IGVhc3QgPSBNYXRoLm1pbihib3VuZHMuZWFzdCwgd2VzdCArIGdyaWRTaXplKTtcbiAgICAgIFxuICAgICAgY29uc3QgY2VsbDogR3JpZENlbGwgPSB7XG4gICAgICAgIGlkOiBgY2VsbC0ke2l9LSR7an1gLFxuICAgICAgICBib3VuZHM6IHsgbm9ydGgsIHNvdXRoLCBlYXN0LCB3ZXN0IH0sXG4gICAgICAgIGNlbnRlcjoge1xuICAgICAgICAgIGxhdDogKG5vcnRoICsgc291dGgpIC8gMixcbiAgICAgICAgICBsbmc6IChlYXN0ICsgd2VzdCkgLyAyLFxuICAgICAgICB9LFxuICAgICAgICBob3RzcG90Q291bnQ6IDAsXG4gICAgICAgIGNvbG9yOiBDT0xPUl9HUkFERVNbMF0uY29sb3IsXG4gICAgICB9O1xuICAgICAgXG4gICAgICBjZWxscy5wdXNoKGNlbGwpO1xuICAgIH1cbiAgfVxuICBcbiAgcmV0dXJuIGNlbGxzO1xufVxuXG4vLyDlsIbng63ngrnmlbDmja7liIbphY3liLDnvZHmoLzkuK1cbmV4cG9ydCBmdW5jdGlvbiBhc3NpZ25Ib3RzcG90c1RvR3JpZChcbiAgY2VsbHM6IEdyaWRDZWxsW10sXG4gIGhvdHNwb3RzOiBIb3RzcG90RGF0YVtdXG4pOiBHcmlkQ2VsbFtdIHtcbiAgLy8g6YeN572u5omA5pyJ572R5qC855qE54Ot54K55pWw6YePXG4gIGNlbGxzLmZvckVhY2goY2VsbCA9PiB7XG4gICAgY2VsbC5ob3RzcG90Q291bnQgPSAwO1xuICB9KTtcbiAgXG4gIC8vIOWwhueDreeCueWIhumFjeWIsOWvueW6lOeahOe9keagvFxuICBob3RzcG90cy5mb3JFYWNoKGhvdHNwb3QgPT4ge1xuICAgIGNvbnN0IGNlbGwgPSBjZWxscy5maW5kKGMgPT4gXG4gICAgICBob3RzcG90LmxhdCA+PSBjLmJvdW5kcy5zb3V0aCAmJlxuICAgICAgaG90c3BvdC5sYXQgPCBjLmJvdW5kcy5ub3J0aCAmJlxuICAgICAgaG90c3BvdC5sbmcgPj0gYy5ib3VuZHMud2VzdCAmJlxuICAgICAgaG90c3BvdC5sbmcgPCBjLmJvdW5kcy5lYXN0XG4gICAgKTtcbiAgICBcbiAgICBpZiAoY2VsbCkge1xuICAgICAgY2VsbC5ob3RzcG90Q291bnQgKz0gaG90c3BvdC5jb3VudDtcbiAgICB9XG4gIH0pO1xuICBcbiAgLy8g5pu05paw572R5qC86aKc6ImyXG4gIGNlbGxzLmZvckVhY2goY2VsbCA9PiB7XG4gICAgY2VsbC5jb2xvciA9IGdldENvbG9yQnlDb3VudChjZWxsLmhvdHNwb3RDb3VudCk7XG4gIH0pO1xuICBcbiAgcmV0dXJuIGNlbGxzO1xufVxuXG4vLyDnlJ/miJDmqKHmi5/ng63ngrnmlbDmja5cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZU1vY2tIb3RzcG90cyhib3VuZHM6IE1hcEJvdW5kcywgY291bnQ6IG51bWJlcik6IEhvdHNwb3REYXRhW10ge1xuICBjb25zdCBob3RzcG90czogSG90c3BvdERhdGFbXSA9IFtdO1xuICBcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb3VudDsgaSsrKSB7XG4gICAgY29uc3QgbGF0ID0gYm91bmRzLnNvdXRoICsgTWF0aC5yYW5kb20oKSAqIChib3VuZHMubm9ydGggLSBib3VuZHMuc291dGgpO1xuICAgIGNvbnN0IGxuZyA9IGJvdW5kcy53ZXN0ICsgTWF0aC5yYW5kb20oKSAqIChib3VuZHMuZWFzdCAtIGJvdW5kcy53ZXN0KTtcbiAgICBjb25zdCBob3RzcG90Q291bnQgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxMDApICsgMTtcbiAgICBcbiAgICBob3RzcG90cy5wdXNoKHtcbiAgICAgIGlkOiBgaG90c3BvdC0ke2l9YCxcbiAgICAgIGxhdCxcbiAgICAgIGxuZyxcbiAgICAgIGNvdW50OiBob3RzcG90Q291bnQsXG4gICAgfSk7XG4gIH1cbiAgXG4gIHJldHVybiBob3RzcG90cztcbn1cblxuLy8g5qC85byP5YyW5pWw5a2X5pi+56S6XG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0Q291bnQoY291bnQ6IG51bWJlcik6IHN0cmluZyB7XG4gIGlmIChjb3VudCA+PSAxMDAwKSB7XG4gICAgcmV0dXJuIGAkeyhjb3VudCAvIDEwMDApLnRvRml4ZWQoMSl9a2A7XG4gIH1cbiAgcmV0dXJuIGNvdW50LnRvU3RyaW5nKCk7XG59XG5cbi8vIOiuoeeul+WcsOWbvui+ueeVjFxuZXhwb3J0IGZ1bmN0aW9uIGNhbGN1bGF0ZUJvdW5kcyhjZW50ZXI6IFtudW1iZXIsIG51bWJlcl0sIHpvb206IG51bWJlcik6IE1hcEJvdW5kcyB7XG4gIC8vIOagueaNrue8qeaUvue6p+WIq+iuoeeul+WQiOmAgueahOi+ueeVjFxuICBjb25zdCBsYXRSYW5nZSA9IDE4MCAvIE1hdGgucG93KDIsIHpvb20gLSAxKTtcbiAgY29uc3QgbG5nUmFuZ2UgPSAzNjAgLyBNYXRoLnBvdygyLCB6b29tIC0gMSk7XG4gIFxuICByZXR1cm4ge1xuICAgIG5vcnRoOiBjZW50ZXJbMF0gKyBsYXRSYW5nZSAvIDIsXG4gICAgc291dGg6IGNlbnRlclswXSAtIGxhdFJhbmdlIC8gMixcbiAgICBlYXN0OiBjZW50ZXJbMV0gKyBsbmdSYW5nZSAvIDIsXG4gICAgd2VzdDogY2VudGVyWzFdIC0gbG5nUmFuZ2UgLyAyLFxuICB9O1xufVxuIl0sIm5hbWVzIjpbIkNPTE9SX0dSQURFUyIsIm1pbiIsIm1heCIsImNvbG9yIiwibGFiZWwiLCJJbmZpbml0eSIsImdldENvbG9yQnlDb3VudCIsImNvdW50IiwiZ3JhZGUiLCJmaW5kIiwiZyIsImdlbmVyYXRlR3JpZCIsImJvdW5kcyIsImdyaWRTaXplIiwiY2VsbHMiLCJsYXRTdGVwcyIsIk1hdGgiLCJjZWlsIiwibm9ydGgiLCJzb3V0aCIsImxuZ1N0ZXBzIiwiZWFzdCIsIndlc3QiLCJpIiwiaiIsImNlbGwiLCJpZCIsImNlbnRlciIsImxhdCIsImxuZyIsImhvdHNwb3RDb3VudCIsInB1c2giLCJhc3NpZ25Ib3RzcG90c1RvR3JpZCIsImhvdHNwb3RzIiwiZm9yRWFjaCIsImhvdHNwb3QiLCJjIiwiZ2VuZXJhdGVNb2NrSG90c3BvdHMiLCJyYW5kb20iLCJmbG9vciIsImZvcm1hdENvdW50IiwidG9GaXhlZCIsInRvU3RyaW5nIiwiY2FsY3VsYXRlQm91bmRzIiwiem9vbSIsImxhdFJhbmdlIiwicG93IiwibG5nUmFuZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/hotspot-utils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Icon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js\");\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_c = (param, ref)=>{\n    let { color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = \"\", children, iconNode, ...rest } = param;\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n        ref,\n        ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide\", className),\n        ...rest\n    }, [\n        ...iconNode.map((param)=>{\n            let [tag, attrs] = param;\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs);\n        }),\n        ...Array.isArray(children) ? children : [\n            children\n        ]\n    ]);\n});\n_c1 = Icon;\n //# sourceMappingURL=Icon.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Icon$forwardRef\");\n$RefreshReg$(_c1, \"Icon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createLucideIcon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/* harmony import */ var _Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Icon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((param, ref)=>{\n        let { className, ...props } = param;\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            ref,\n            iconNode,\n            className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide-\".concat((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toKebabCase)(iconName)), className),\n            ...props\n        });\n    });\n    Component.displayName = \"\".concat(iconName);\n    return Component;\n};\n //# sourceMappingURL=createLucideIcon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC40NTQuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9jcmVhdGVMdWNpZGVJY29uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXTSx1QkFBbUIsR0FBQyxVQUFrQixRQUF1QjtJQUNqRSxNQUFNLENBQVksbUZBQWlFO1lBQXpCLEVBQUUsQ0FBVyxXQUFHLFFBQVM7NkJBQ2pGLG9EQUFhLENBQUMsZ0RBQU07WUFDbEI7WUFDQTtZQUNBLFdBQVcsa0VBQWEsV0FBK0IsT0FBckIsaUVBQVcsQ0FBQyxRQUFRLENBQUMsR0FBSSxTQUFTO1lBQ3BFLEdBQUc7UUFBQSxDQUNKOztJQUdPLHdCQUFjLENBQUcsRUFBUSxPQUFSLFFBQVE7SUFFNUI7QUFDVCIsInNvdXJjZXMiOlsiQzpcXHdvcmtzcGFjZXNcXGRkXFxzcmNcXGNyZWF0ZUx1Y2lkZUljb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlRWxlbWVudCwgZm9yd2FyZFJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IG1lcmdlQ2xhc3NlcywgdG9LZWJhYkNhc2UgfSBmcm9tICdAbHVjaWRlL3NoYXJlZCc7XG5pbXBvcnQgeyBJY29uTm9kZSwgTHVjaWRlUHJvcHMgfSBmcm9tICcuL3R5cGVzJztcbmltcG9ydCBJY29uIGZyb20gJy4vSWNvbic7XG5cbi8qKlxuICogQ3JlYXRlIGEgTHVjaWRlIGljb24gY29tcG9uZW50XG4gKiBAcGFyYW0ge3N0cmluZ30gaWNvbk5hbWVcbiAqIEBwYXJhbSB7YXJyYXl9IGljb25Ob2RlXG4gKiBAcmV0dXJucyB7Rm9yd2FyZFJlZkV4b3RpY0NvbXBvbmVudH0gTHVjaWRlSWNvblxuICovXG5jb25zdCBjcmVhdGVMdWNpZGVJY29uID0gKGljb25OYW1lOiBzdHJpbmcsIGljb25Ob2RlOiBJY29uTm9kZSkgPT4ge1xuICBjb25zdCBDb21wb25lbnQgPSBmb3J3YXJkUmVmPFNWR1NWR0VsZW1lbnQsIEx1Y2lkZVByb3BzPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT5cbiAgICBjcmVhdGVFbGVtZW50KEljb24sIHtcbiAgICAgIHJlZixcbiAgICAgIGljb25Ob2RlLFxuICAgICAgY2xhc3NOYW1lOiBtZXJnZUNsYXNzZXMoYGx1Y2lkZS0ke3RvS2ViYWJDYXNlKGljb25OYW1lKX1gLCBjbGFzc05hbWUpLFxuICAgICAgLi4ucHJvcHMsXG4gICAgfSksXG4gICk7XG5cbiAgQ29tcG9uZW50LmRpc3BsYXlOYW1lID0gYCR7aWNvbk5hbWV9YDtcblxuICByZXR1cm4gQ29tcG9uZW50O1xufTtcblxuZXhwb3J0IGRlZmF1bHQgY3JlYXRlTHVjaWRlSWNvbjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultAttributes)\n/* harmony export */ });\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC40NTQuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9kZWZhdWx0QXR0cmlidXRlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7SUFBQSxDQUFlO0lBQ2IsS0FBTztJQUNQLEtBQU87SUFDUCxNQUFRO0lBQ1IsT0FBUztJQUNULElBQU07SUFDTixNQUFRO0lBQ1IsV0FBYTtJQUNiLGFBQWU7SUFDZixjQUFnQjtBQUNsQiIsInNvdXJjZXMiOlsiQzpcXHdvcmtzcGFjZXNcXGRkXFxzcmNcXGRlZmF1bHRBdHRyaWJ1dGVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcbiAgeG1sbnM6ICdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycsXG4gIHdpZHRoOiAyNCxcbiAgaGVpZ2h0OiAyNCxcbiAgdmlld0JveDogJzAgMCAyNCAyNCcsXG4gIGZpbGw6ICdub25lJyxcbiAgc3Ryb2tlOiAnY3VycmVudENvbG9yJyxcbiAgc3Ryb2tlV2lkdGg6IDIsXG4gIHN0cm9rZUxpbmVjYXA6ICdyb3VuZCcsXG4gIHN0cm9rZUxpbmVqb2luOiAncm91bmQnLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/activity.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/activity.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Activity)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Activity = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Activity\", [\n    [\n        \"path\",\n        {\n            d: \"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2\",\n            key: \"169zse\"\n        }\n    ]\n]);\n //# sourceMappingURL=activity.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/activity.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/layers.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/layers.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layers)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Layers = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Layers\", [\n    [\n        \"path\",\n        {\n            d: \"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z\",\n            key: \"8b97xw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65\",\n            key: \"dd6zsq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65\",\n            key: \"ep9fru\"\n        }\n    ]\n]);\n //# sourceMappingURL=layers.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC40NTQuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9sYXllcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFhTSxlQUFTLGdFQUFnQixDQUFDLFFBQVU7SUFDeEM7UUFDRTtRQUNBO1lBQ0UsQ0FBRztZQUNILEdBQUs7UUFDUDtLQUNGO0lBQ0E7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQWdEO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUM3RTtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBZ0Q7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQzlFIiwic291cmNlcyI6WyJDOlxcd29ya3NwYWNlc1xcc3JjXFxpY29uc1xcbGF5ZXJzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgTGF5ZXJzXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSnRNVEl1T0RNZ01pNHhPR0V5SURJZ01DQXdJREF0TVM0Mk5pQXdUREl1TmlBMkxqQTRZVEVnTVNBd0lEQWdNQ0F3SURFdU9ETnNPQzQxT0NBekxqa3hZVElnTWlBd0lEQWdNQ0F4TGpZMklEQnNPQzQxT0MwekxqbGhNU0F4SURBZ01DQXdJREF0TVM0NE0xb2lJQzgrQ2lBZ1BIQmhkR2dnWkQwaWJUSXlJREUzTGpZMUxUa3VNVGNnTkM0eE5tRXlJRElnTUNBd0lERXRNUzQyTmlBd1RESWdNVGN1TmpVaUlDOCtDaUFnUEhCaGRHZ2daRDBpYlRJeUlERXlMalkxTFRrdU1UY2dOQzR4Tm1FeUlESWdNQ0F3SURFdE1TNDJOaUF3VERJZ01USXVOalVpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvbGF5ZXJzXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgTGF5ZXJzID0gY3JlYXRlTHVjaWRlSWNvbignTGF5ZXJzJywgW1xuICBbXG4gICAgJ3BhdGgnLFxuICAgIHtcbiAgICAgIGQ6ICdtMTIuODMgMi4xOGEyIDIgMCAwIDAtMS42NiAwTDIuNiA2LjA4YTEgMSAwIDAgMCAwIDEuODNsOC41OCAzLjkxYTIgMiAwIDAgMCAxLjY2IDBsOC41OC0zLjlhMSAxIDAgMCAwIDAtMS44M1onLFxuICAgICAga2V5OiAnOGI5N3h3JyxcbiAgICB9LFxuICBdLFxuICBbJ3BhdGgnLCB7IGQ6ICdtMjIgMTcuNjUtOS4xNyA0LjE2YTIgMiAwIDAgMS0xLjY2IDBMMiAxNy42NScsIGtleTogJ2RkNnpzcScgfV0sXG4gIFsncGF0aCcsIHsgZDogJ20yMiAxMi42NS05LjE3IDQuMTZhMiAyIDAgMCAxLTEuNjYgMEwyIDEyLjY1Jywga2V5OiAnZXA5ZnJ1JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBMYXllcnM7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/layers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map-pin.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map-pin.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MapPin)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst MapPin = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"MapPin\", [\n    [\n        \"path\",\n        {\n            d: \"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0\",\n            key: \"1r0f0z\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"10\",\n            r: \"3\",\n            key: \"ilqhr7\"\n        }\n    ]\n]);\n //# sourceMappingURL=map-pin.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Map)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Map = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Map\", [\n    [\n        \"path\",\n        {\n            d: \"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z\",\n            key: \"169xi5\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15 5.764v15\",\n            key: \"1pn4in\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 3.236v15\",\n            key: \"1uimfh\"\n        }\n    ]\n]);\n //# sourceMappingURL=map.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mountain.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mountain.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Mountain)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Mountain = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Mountain\", [\n    [\n        \"path\",\n        {\n            d: \"m8 3 4 8 5-5 5 15H2L8 3z\",\n            key: \"otkl63\"\n        }\n    ]\n]);\n //# sourceMappingURL=mountain.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC40NTQuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9tb3VudGFpbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFNLGlCQUFXLGdFQUFnQixDQUFDLFVBQVk7SUFDNUM7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQTRCO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtDQUMxRCIsInNvdXJjZXMiOlsiQzpcXHdvcmtzcGFjZXNcXHNyY1xcaWNvbnNcXG1vdW50YWluLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgTW91bnRhaW5cbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE9DQXpJRFFnT0NBMUxUVWdOU0F4TlVneVREZ2dNM29pSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvbW91bnRhaW5cbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBNb3VudGFpbiA9IGNyZWF0ZUx1Y2lkZUljb24oJ01vdW50YWluJywgW1xuICBbJ3BhdGgnLCB7IGQ6ICdtOCAzIDQgOCA1LTUgNSAxNUgyTDggM3onLCBrZXk6ICdvdGtsNjMnIH1dLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IE1vdW50YWluO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mountain.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/satellite.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/satellite.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Satellite)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Satellite = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Satellite\", [\n    [\n        \"path\",\n        {\n            d: \"M13 7 9 3 5 7l4 4\",\n            key: \"vyckw6\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m17 11 4 4-4 4-4-4\",\n            key: \"rchckc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m8 12 4 4 6-6-4-4Z\",\n            key: \"1sshf7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m16 8 3-3\",\n            key: \"x428zp\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 21a6 6 0 0 0-6-6\",\n            key: \"1iajcf\"\n        }\n    ]\n]);\n //# sourceMappingURL=satellite.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC40NTQuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9zYXRlbGxpdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFhTSxrQkFBWSxnRUFBZ0IsQ0FBQyxXQUFhO0lBQzlDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFxQjtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDbEQ7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQXNCO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUNuRDtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBc0I7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQ25EO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFhO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUMxQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBdUI7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQ3JEIiwic291cmNlcyI6WyJDOlxcd29ya3NwYWNlc1xcc3JjXFxpY29uc1xcc2F0ZWxsaXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgU2F0ZWxsaXRlXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVE1nTnlBNUlETWdOU0EzYkRRZ05DSWdMejRLSUNBOGNHRjBhQ0JrUFNKdE1UY2dNVEVnTkNBMExUUWdOQzAwTFRRaUlDOCtDaUFnUEhCaGRHZ2daRDBpYlRnZ01USWdOQ0EwSURZdE5pMDBMVFJhSWlBdlBnb2dJRHh3WVhSb0lHUTlJbTB4TmlBNElETXRNeUlnTHo0S0lDQThjR0YwYUNCa1BTSk5PU0F5TVdFMklEWWdNQ0F3SURBdE5pMDJJaUF2UGdvOEwzTjJaejRLKSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9zYXRlbGxpdGVcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBTYXRlbGxpdGUgPSBjcmVhdGVMdWNpZGVJY29uKCdTYXRlbGxpdGUnLCBbXG4gIFsncGF0aCcsIHsgZDogJ00xMyA3IDkgMyA1IDdsNCA0Jywga2V5OiAndnlja3c2JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnbTE3IDExIDQgNC00IDQtNC00Jywga2V5OiAncmNoY2tjJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnbTggMTIgNCA0IDYtNi00LTRaJywga2V5OiAnMXNzaGY3JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnbTE2IDggMy0zJywga2V5OiAneDQyOHpwJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTkgMjFhNiA2IDAgMCAwLTYtNicsIGtleTogJzFpYWpjZicgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgU2F0ZWxsaXRlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/satellite.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Search)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Search = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Search\", [\n    [\n        \"circle\",\n        {\n            cx: \"11\",\n            cy: \"11\",\n            r: \"8\",\n            key: \"4ej97u\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m21 21-4.3-4.3\",\n            key: \"1qie3q\"\n        }\n    ]\n]);\n //# sourceMappingURL=search.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ X)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst X = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"X\", [\n    [\n        \"path\",\n        {\n            d: \"M18 6 6 18\",\n            key: \"1bl5f8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6 6 12 12\",\n            key: \"d8bk6v\"\n        }\n    ]\n]);\n //# sourceMappingURL=x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC40NTQuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy94LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sVUFBSSxnRUFBZ0IsQ0FBQyxHQUFLO0lBQzlCO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFjO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUMzQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBYztZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7Q0FDNUMiLCJzb3VyY2VzIjpbIkM6XFx3b3Jrc3BhY2VzXFxzcmNcXGljb25zXFx4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgWFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTVRnZ05pQTJJREU0SWlBdlBnb2dJRHh3WVhSb0lHUTlJbTAySURZZ01USWdNVElpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMveFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IFggPSBjcmVhdGVMdWNpZGVJY29uKCdYJywgW1xuICBbJ3BhdGgnLCB7IGQ6ICdNMTggNiA2IDE4Jywga2V5OiAnMWJsNWY4JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnbTYgNiAxMiAxMicsIGtleTogJ2Q4Yms2dicgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgWDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeClasses: () => (/* binding */ mergeClasses),\n/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase)\n/* harmony export */ });\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst mergeClasses = function() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter((className, index, array)=>{\n        return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n    }).join(\" \").trim();\n};\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Ctest-hotspots%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Ctest-hotspots%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/test-hotspots/page.tsx */ \"(app-pages-browser)/./app/test-hotspots/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjIuNF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q3dvcmtzcGFjZXMlNUMlNUNkZCU1QyU1Q21iZHAlNUMlNUNmcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q3Rlc3QtaG90c3BvdHMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUFxRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcd29ya3NwYWNlc1xcXFxkZFxcXFxtYmRwXFxcXGZyb250ZW5kXFxcXGFwcFxcXFx0ZXN0LWhvdHNwb3RzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Ctest-hotspots%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          },\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        Error(\"react-stack-top-frame\"),\n        createTask(getTaskName(type))\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \*********************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjIuNF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx5UUFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFx3b3Jrc3BhY2VzXFxkZFxcbWJkcFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG5leHRAMTUuMi40X3JlYWN0LWRvbUAxOS4xLjBfcmVhY3RAMTkuMS4wX19yZWFjdEAxOS4xLjBcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cworkspaces%5C%5Cdd%5C%5Cmbdp%5C%5Cfrontend%5C%5Capp%5C%5Ctest-hotspots%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);